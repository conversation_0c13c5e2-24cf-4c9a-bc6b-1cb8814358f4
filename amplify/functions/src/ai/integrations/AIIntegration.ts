/**
 * AIIntegration.ts
 *
 * This file contains the AIService class which provides integration with various AI services:
 * - Azure OpenAI for text and image analysis
 * - Google Gemini for text analysis, image analysis, and image generation
 * - DALL-E for image generation
 *
 * TODO : We can split the file based on provider : OpenAI or Gemini
 */
import { GoogleGenAI } from "@google/genai";
import { AxiosClient } from "../../config/axiosClient";
import { withRetry, RetryConfig } from "../../utils/retryUtil";

// Custom retry configuration for AI calls
const AI_RETRY_CONFIG: RetryConfig = {
  maxRetries: 1,
  backoffMultiplier: 1.5
};

export class AIService {
  private apiVersion: string;
  private deploymentModel: string;
  private endpoint: string;
  private apiKey: string;
  private aiClient;
  private dalleClient;
  private geminiApiKey: string;
  private geminiAI: GoogleGenAI;

  constructor() {
    // Load configuration from environment variables
    this.apiVersion = process.env.AZURE_OPENAI_API_VERSION || "2024-06-01";
    this.endpoint =
      process.env.AZURE_OPENAI_ENDPOINT ||
      "https://whatsapp-ai-stylist-monova.openai.azure.com/openai/deployments";

    this.apiKey =
      process.env.AZURE_OPENAI_API_KEY || "********************************";
    this.deploymentModel =
      process.env.AZURE_OPENAI_DEPLOYMENT_MODEL || "gpt-4o-mini-2";

    // Initialize AI client
    this.aiClient = AxiosClient.getInstance(this.endpoint).getClient();
    this.dalleClient = AxiosClient.getInstance(
      "https://whatsapp-ai-stylist-monova.openai.azure.com/openai/deployments/dall-e-3"
    ).getClient();

    this.geminiApiKey =
      process.env.GEMINI_API_KEY || "AIzaSyA_PCT9C7-tmQmlnwjByEZIcYr0EkwcAt8";
    this.geminiAI = new GoogleGenAI({ apiKey: this.geminiApiKey });
  }

  async getGeminiTextAnalysis(
    userPrompt: string,
    systemPrompt: string,
    temperature: number = 0.7
  ): Promise<string> {
    try {
      console.log("[AIService] Analyzing text with Gemini:", {
        promptLength: userPrompt.length,
        systemPromptLength: systemPrompt.length,
      });

      // Create a complete prompt that includes the system instructions
      const fullPrompt = `${systemPrompt}\n\n${userPrompt}`;

      // Configure generation parameters
      const generationConfig = {
        temperature: temperature,
        topP: 0.95,
        topK: 40,
        maxOutputTokens: 10000,
      };

      // Use withRetry to handle potential failures with exponential backoff
      const response = await withRetry(
        async () => {
          console.log("[AIService] Sending request to Gemini for text analysis");
          return this.geminiAI.models.generateContent({
            model: "gemini-2.0-flash", // Using Gemini 2.0 Flash for faster responses
            contents: [{ text: fullPrompt }],
            config: generationConfig
          });
        },
        AI_RETRY_CONFIG
      );

      // Get the text response
      const responseText = response.text || '';

      console.log("[AIService] Gemini text analysis completed successfully", {
        responseLength: responseText.length,
      });

      return responseText;
    } catch (error) {
      console.error("[AIService] Error analyzing text with Gemini:", {
        error,
        promptLength: userPrompt.length,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }

  /**
   * Get structured output from Gemini using an image and a defined JSON schema
   * @param imageUrl The URL of the image to analyze
   * @param prompt The prompt to send to Gemini
   * @param systemPrompt The system prompt to guide the model
   * @param schema The JSON schema to use for structured output
   * @param temperature The temperature to use for generation (default: 0.3)
   * @returns The structured output as a JSON object
   */
  async getGeminiImageStructuredOutput<T>(
    imageUrl: string,
    prompt: string,
    systemPrompt: string,
    schema: object,
    temperature: number = 0.3
  ): Promise<T> {
    try {
      console.log("[AIService][getGeminiImageStructuredOutput] Getting structured output from Gemini for image:", {
        imageUrl: imageUrl.substring(0, 30) + '...',
        promptLength: prompt.length,
        systemPromptLength: systemPrompt.length,
        schemaKeys: Object.keys(schema),
      });

      // Fetch the image as binary data
      const imageResponse = await fetch(imageUrl);
      if (!imageResponse.ok) {
        throw new Error(`Failed to fetch image: ${imageResponse.status} ${imageResponse.statusText}`);
      }

      const imageArrayBuffer = await imageResponse.arrayBuffer();
      const imageData = new Uint8Array(imageArrayBuffer);

      // Determine the mime type based on the image URL
      const mimeType = this.getMimeTypeFromUrl(imageUrl);

      // Create a complete prompt that includes the system instructions
      const fullPrompt = `${systemPrompt}\n\n${prompt}`;

      console.log("[AIService][getGeminiImageStructuredOutput] Sending request to Gemini with image and schema");

      // Convert image to base64
      const base64Image = Buffer.from(imageData).toString("base64");

      // Use the structured output feature with the provided schema in the config
      // Use withRetry to handle potential failures with exponential backoff
      const response = await withRetry(
        async () => {
          console.log("[AIService][getGeminiImageStructuredOutput] Sending request to Gemini with retry capability");
          return this.geminiAI.models.generateContent({
            model: 'gemini-2.0-flash',
            contents: [
              {
                role: 'user',
                parts: [
                  {
                    inlineData: {
                      mimeType: mimeType,
                      data: base64Image
                    }
                  },
                  { text: fullPrompt }
                ]
              }
            ],
            config: {
              temperature: temperature,
              topP: 0.95,
              topK: 40,
              maxOutputTokens: 10000,
              responseMimeType: 'application/json',
              responseSchema: schema
            }
          });
        },
        AI_RETRY_CONFIG
      );

      // Get the text response and parse it as JSON
      const responseText = response.text || '{}';

      // Parse the JSON response
      const structuredOutput = JSON.parse(responseText);

      // Log token usage from the response metadata if available
      if (response.usageMetadata) {
        const promptTokens = response.usageMetadata.promptTokenCount || 0;
        const candidateTokens = response.usageMetadata.candidatesTokenCount || 0;

        console.log("[AIService][getGeminiImageStructuredOutput] Token usage metadata:", {
          promptTokenCount: promptTokens,
          candidatesTokenCount: candidateTokens,
          totalTokenCount: promptTokens + candidateTokens
        });
      }

      console.log("[AIService][getGeminiImageStructuredOutput] Successfully got structured output:", {
        outputKeys: Object.keys(structuredOutput),
      });

      return structuredOutput as T;
    } catch (error) {
      console.error("[AIService][getGeminiImageStructuredOutput] Error getting structured output:", {
        error,
        imageUrl: imageUrl.substring(0, 30) + '...',
        errorMessage: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined,
      });
      throw error;
    }
  }

  /**
   * Get structured output from Gemini for text analysis using a defined JSON schema
   * @param userPrompt The user input string
   * @param systemPrompt The system prompt to guide the model
   * @param schema The JSON schema to use for structured output
   * @param temperature The temperature to use for generation (default: 0.3)
   * @returns The structured output as a JSON object
   */
  async getGeminiTextAnalysisStructuredOutput<T>(
    userPrompt: string,
    systemPrompt: string,
    schema: object,
    temperature: number = 0.3
  ): Promise<T> {
    try {
      console.log("[AIService][getGeminiTextAnalysisStructuredOutput] Getting structured output from Gemini for text:", {
        promptLength: userPrompt.length,
        systemPromptLength: systemPrompt.length,
        schemaKeys: Object.keys(schema),
      });

      // Create a complete prompt that includes the system instructions
      const fullPrompt = `${systemPrompt}\n\n${userPrompt}`;

      // Use the structured output feature with the provided schema in the config
      // Use withRetry to handle potential failures with exponential backoff
      const response = await withRetry(
        async () => {
          console.log("[AIService][getGeminiTextAnalysisStructuredOutput] Sending request to Gemini with retry capability");
          return this.geminiAI.models.generateContent({
            model: 'gemini-2.0-flash',
            contents: [
              { text: fullPrompt }
            ],
            config: {
              temperature: temperature,
              topP: 0.95,
              topK: 40,
              maxOutputTokens: 2048,
              responseMimeType: 'application/json',
              responseSchema: schema
            }
          });
        },
        AI_RETRY_CONFIG
      );

      // Get the text response and parse it as JSON
      const responseText = response.text || '{}';
      const structuredOutput = JSON.parse(responseText);

      // Log token usage from the response metadata if available
      if (response.usageMetadata) {
        const promptTokens = response.usageMetadata.promptTokenCount || 0;
        const candidateTokens = response.usageMetadata.candidatesTokenCount || 0;

        console.log("[AIService][getGeminiTextAnalysisStructuredOutput] Token usage metadata:", {
          promptTokenCount: promptTokens,
          candidatesTokenCount: candidateTokens,
          totalTokenCount: promptTokens + candidateTokens
        });
      }

      console.log("[AIService][getGeminiTextAnalysisStructuredOutput] Successfully got structured output:", {
        outputKeys: Object.keys(structuredOutput),
      });

      return structuredOutput as T;
    } catch (error) {
      console.error("[AIService][getGeminiTextAnalysisStructuredOutput] Error getting structured output:", {
        error,
        promptLength: userPrompt.length,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined,
      });
      throw error;
    }
  }

  async getImageAnalysis(
    imageUrl: string,
    additionalDetails: string,
    systemPrompt: string,
    model: string = this.deploymentModel
  ): Promise<string> {
    try {
      console.log("[AIService] Analyzing image:", {
        imageUrl,
        hasAdditionalDetails: !!additionalDetails,
      });

      const messages = [
        {
          role: "system",
          content: systemPrompt,
        },
        {
          role: "user",
          content: [
            {
              type: "image_url",
              image_url: { url: imageUrl },
            },
            {
              type: "text",
              text: additionalDetails,
            },
          ],
        },
      ];

      console.log("[AIService] Sending request to AI service:", {
        imageUrl,
        model,
        messages,
      });

      const url = `/${model}/chat/completions?api-version=${this.apiVersion}`;

      // Use withRetry to handle potential failures with exponential backoff
      const response = await withRetry(
        async () => {
          console.log("[AIService] Sending request to Azure OpenAI with retry capability");
          return this.aiClient.post(
            url,
            {
              messages,
              max_tokens: 800,
              temperature: 0.3, // Lower temperature for more deterministic outputs
            },
            {
              headers: {
                "api-key": this.apiKey,
                "Content-Type": "application/json",
              },
            }
          );
        },
        AI_RETRY_CONFIG
      );

      if (!response.data.choices || response.data.choices.length === 0) {
        throw new Error("No response from AI service");
      }

      console.log("[AIService] Analysis completed successfully");
      return response.data.choices[0].message.content;
    } catch (error) {
      console.error("[AIService] Error analyzing image:", {
        error,
        imageUrl,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }

  // New method to use Gemini for image analysis
  async getGeminiImageAnalysis(
    imageUrl: string,
    additionalDetails: string,
    systemPrompt: string
  ): Promise<string> {
    try {
      console.log("[AIService] Analyzing image with Gemini:", {
        imageUrl,
        hasAdditionalDetails: !!additionalDetails,
      });

      // Fetch the image as binary data
      const imageResponse = await fetch(imageUrl);
      if (!imageResponse.ok) {
        throw new Error(`Failed to fetch image: ${imageResponse.status} ${imageResponse.statusText}`);
      }

      const imageArrayBuffer = await imageResponse.arrayBuffer();
      const imageData = new Uint8Array(imageArrayBuffer);

      // Determine the mime type based on the image URL
      const mimeType = this.getMimeTypeFromUrl(imageUrl);

      // Create a complete prompt that includes the system instructions
      const fullPrompt = `${systemPrompt}\n\n${additionalDetails}`;

      // Convert image to base64
      const base64Image = Buffer.from(imageData).toString("base64");

      // Use the Gemini AI client to generate content with retry capability
      const response = await withRetry(
        async () => {
          console.log("[AIService] Sending request to Gemini for image analysis with retry capability");
          return this.geminiAI.models.generateContent({
            model: 'gemini-2.0-flash',
            contents: [
              {
                role: 'user',
                parts: [
                  {
                    inlineData: {
                      mimeType: mimeType,
                      data: base64Image
                    }
                  },
                  { text: fullPrompt }
                ]
              }
            ],
            config: {
              temperature: 0.3,
              topP: 0.95,
              topK: 40,
              maxOutputTokens: 1024
            }
          });
        },
        AI_RETRY_CONFIG
      );

      // Get the text response
      const responseText = response.text || '';

      console.log("[AIService] Gemini image analysis completed successfully", {
        responseLength: responseText.length,
      });

      return responseText;
    } catch (error) {
      console.error("[AIService] Error analyzing image with Gemini:", {
        error,
        imageUrl: imageUrl.substring(0, 30) + '...',
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }

  /**
   * Generate an image using Gemini's multimodal capabilities
   * @param imageUrl URL of the source image
   * @param prompt Text prompt describing the desired image transformation
   * @param systemPrompt System prompt to guide the image generation
   * @returns Object containing the generated image data and any text response
   */
  async generateImageUsingGeminiMultiModal(
    imageUrl: string,
    prompt: string,
    systemPrompt: string
  ): Promise<{
    imageData?: Buffer;
    text?: string;
  }> {
    try {
      console.log("[AIService][generateImageUsingGeminiMultiModal] Starting image generation:", {
        imageUrl,
        promptLength: prompt.length,
        systemPromptLength: systemPrompt.length,
        timestamp: new Date().toISOString()
      });

      // Fetch the image as binary data
      console.log(`[AIService][generateImageUsingGeminiMultiModal] Fetching image from URL: ${imageUrl}`);
      const imageResponse = await fetch(imageUrl);

      if (!imageResponse.ok) {
        console.error(`[AIService][generateImageUsingGeminiMultiModal] Failed to fetch image: ${imageResponse.status} ${imageResponse.statusText}`);
        throw new Error(`Failed to fetch image: ${imageResponse.status} ${imageResponse.statusText}`);
      }

      const imageArrayBuffer = await imageResponse.arrayBuffer();
      console.log(`[AIService][generateImageUsingGeminiMultiModal] Image fetched successfully, size: ${imageArrayBuffer.byteLength} bytes`);
      const imageData = new Uint8Array(imageArrayBuffer);

      // Determine the mime type based on the image URL
      const mimeType = this.getMimeTypeFromUrl(imageUrl);
      console.log(`[AIService][generateImageUsingGeminiMultiModal] Detected MIME type: ${mimeType}`);

      // Use the GoogleGenAI SDK for image generation
      console.log(`[AIService][generateImageUsingGeminiMultiModal] Preparing API request to Gemini using SDK`);

      // Convert the image data to base64
      const base64Image = Buffer.from(imageData).toString("base64");
      console.log(`[AIService][generateImageUsingGeminiMultiModal] Converted image to base64, length: ${base64Image.length}`);

      // Use the already initialized Gemini AI client
      console.log(`[AIService][generateImageUsingGeminiMultiModal] Using initialized GoogleGenAI client`);

      // Prepare the content parts as an array of Part objects
      const contentParts = [
        { text: prompt },
        {
          inlineData: {
            mimeType: mimeType,
            data: base64Image,
          },
        },
      ];

      console.log(`[AIService][generateImageUsingGeminiMultiModal] Prepared content parts:`, {
        contentPartsCount: contentParts.length,
        promptPreview: prompt.substring(0, 50) + '...',
        mimeType: mimeType
      });

      // Set generation config with responseModalities to include "Image"
      const generationConfig = {
        responseModalities: ["Text", "Image"], // Required for image
      };

      console.log(`[AIService][generateImageUsingGeminiMultiModal] Calling Gemini image generation model`);

      // Use the Gemini AI client to generate content with the contentParts and config
      // Add retry capability for image generation
      const response = await withRetry(
        async () => {
          console.log("[AIService][generateImageUsingGeminiMultiModal] Sending request to Gemini for image generation with retry capability");
          return this.geminiAI.models.generateContent({
            model: "gemini-2.0-flash-exp-image-generation",
            contents: [
              {
                role: "user",
                parts: contentParts
              }
            ],
            config: {
              responseModalities: generationConfig.responseModalities
            }
          });
        },
        AI_RETRY_CONFIG
      );

      console.log(`[AIService][generateImageUsingGeminiMultiModal] Received response from Gemini API`);

      // Process the response
      let resultText: string | undefined;
      let resultImageData: Buffer | undefined;

      // Check if we have a valid response
      if (!response) {
        console.warn(`[AIService][generateImageUsingGeminiMultiModal] No valid response received`);
        return { text: "No response from Gemini API" };
      }

      // Process the response parts
      console.log(`[AIService][generateImageUsingGeminiMultiModal] Processing response parts`);

      try {
        // Extract text and image data from the response
        // The response structure has changed in newer versions of the SDK
        const text = response.text || '';
        if (text) {
          resultText = text;
          console.log(`[AIService][generateImageUsingGeminiMultiModal] Found text in response: ${text.substring(0, 100)}...`);
        }

        // Check for image data in the response
        // The SDK doesn't have a consistent way to extract images, so we'll use a workaround
        try {
          // Try to access the raw response data
          const rawResponse = JSON.stringify(response);

          // Look for base64 image data in the response
          const base64Match = rawResponse.match(/"data":"([A-Za-z0-9+/=]+)"/);
          if (base64Match && base64Match[1]) {
            resultImageData = Buffer.from(base64Match[1], "base64");
            console.log(`[AIService][generateImageUsingGeminiMultiModal] Found image data in response, size: ${resultImageData.length} bytes`);
          } else {
            console.log(`[AIService][generateImageUsingGeminiMultiModal] No image data found in response`);

            // If we can't find image data, we'll need to use a different approach
            // For now, we'll return the text response and log a warning
            console.warn(`[AIService][generateImageUsingGeminiMultiModal] Image generation may require a different approach`);
          }
        } catch (imageError) {
          console.warn(`[AIService][generateImageUsingGeminiMultiModal] Error extracting image data:`, {
            error: imageError instanceof Error ? imageError.message : 'Unknown error'
          });
        }

        if (!resultText && !resultImageData) {
          console.warn(`[AIService][generateImageUsingGeminiMultiModal] No content found in response`);
        }
      } catch (parseError) {
        console.error(`[AIService][generateImageUsingGeminiMultiModal] Error parsing response:`, {
          error: parseError instanceof Error ? parseError.message : 'Unknown error',
          stack: parseError instanceof Error ? parseError.stack : undefined
        });
      }

      console.log("[AIService] Gemini image generation completed successfully", {
        hasText: !!resultText,
        hasImage: !!resultImageData,
      });

      return {
        imageData: resultImageData,
        text: resultText,
      };
    } catch (error) {
      console.error("[AIService] Error generating image with Gemini:", {
        error,
        imageUrl,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }

  /**
   * Helper method to determine MIME type from URL
   */
  private getMimeTypeFromUrl(url: string): string {
    const extension = url.split(".").pop()?.toLowerCase();
    switch (extension) {
      case "jpg":
      case "jpeg":
        return "image/jpeg";
      case "png":
        return "image/png";
      case "gif":
        return "image/gif";
      case "webp":
        return "image/webp";
      default:
        return "image/jpeg"; // Default to JPEG if unknown
    }
  }

  async generateImage(prompt: string): Promise<string> {
    try {
      console.log("[AIService] Generating image with DALL-E:", {
        promptLength: prompt.length,
      });

      // Use withRetry to handle potential failures with exponential backoff
      const response = await withRetry(
        async () => {
          console.log("[AIService] Sending request to DALL-E with retry capability");
          return this.dalleClient.post(
            "/images/generations?api-version=2024-06-01",
            {
              prompt,
              // size: "1024x1024",
              n: 1,
              quality: "standard",
              style: "vivid",
            },
            {
              headers: {
                "api-key": this.apiKey,
                "Content-Type": "application/json",
              },
            }
          );
        },
        AI_RETRY_CONFIG
      );

      console.log("[AIService] Image generation completed successfully");
      return response.data.data[0].url;
    } catch (error) {
      console.error("[AIService] Error generating image with DALL-E:", {
        error,
        promptLength: prompt.length,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }

  async getAIResponse(
    userPrompt: string[],
    systemPrompt: string,
    model: string = this.deploymentModel,
    messageId?: string
  ): Promise<string> {
    try {
      const messages = [
        {
          role: "system",
          content: systemPrompt,
        },
      ];

      userPrompt.forEach((prompt) => {
        messages.push({
          role: "user",
          content: prompt,
        });
      });

      console.log(`[AIService] Sending request to ${model}:`, {
        messageId,
        promptCount: userPrompt.length,
      });

      const url = `/${model}/chat/completions?api-version=${this.apiVersion}`;

      // Use withRetry to handle potential failures with exponential backoff
      const response = await withRetry(
        async () => {
          console.log("[AIService] Sending request to Azure OpenAI with retry capability");
          return this.aiClient.post(
            url,
            {
              messages,
              temperature: 0.7,
              max_tokens: 800,
              top_p: 0.95,
              frequency_penalty: 0,
              presence_penalty: 0,
              stop: null,
            },
            {
              headers: {
                "api-key": this.apiKey,
                "Content-Type": "application/json",
              },
            }
          );
        },
        AI_RETRY_CONFIG
      );

      if (!response.data.choices || response.data.choices.length === 0) {
        throw new Error("No response from AI service");
      }

      console.log("[AIService] Response received successfully", {
        messageId,
        contentLength: response.data.choices[0].message.content.length,
      });

      return response.data.choices[0].message.content;
    } catch (error) {
      console.error("[AIService] Error getting AI response:", {
        error,
        messageId,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }
}
