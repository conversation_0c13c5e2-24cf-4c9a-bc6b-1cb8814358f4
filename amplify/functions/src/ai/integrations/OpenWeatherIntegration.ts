/**
 * OpenWeatherIntegration.ts
 *
 * This file contains the OpenWeatherService class which provides integration with the OpenWeather API:
 * - Get weather data by city name
 * - Get weather data by geographic coordinates (latitude and longitude)
 * - Get city name from geographic coordinates (reverse geocoding)
 */
import { withRetry, RetryConfig } from "../../utils/retryUtil";

// Custom retry configuration for API calls
const WEATHER_RETRY_CONFIG: RetryConfig = {
  maxRetries: 1,
  backoffMultiplier: 1.5
};

// Full weather data interface (for internal use)
interface FullWeatherResponse {
  weather: Array<{
    description: string;
    main: string;
    icon: string;
  }>;
  name: string;
  sys: {
    country: string;
  };
  main: {
    temp: number;
  };
}

// Simplified weather response interface
export interface SimpleWeatherResponse {
  description: string;
  location: string;
}

// Reverse geocoding response interface
interface ReverseGeocodingResponse {
  name: string;
  country: string;
  state?: string;
}

// City location response interface
export interface CityLocationResponse {
  cityName: string;
  fullLocation: string;
}

export class OpenWeatherService {
  private apiKey: string;
  private baseUrl: string;
  private geoBaseUrl: string;

  constructor() {
    // Load configuration from environment variables
    this.apiKey = process.env.OPENWEATHER_API_KEY || "********************************";
    this.baseUrl = "https://api.openweathermap.org/data/2.5";
    this.geoBaseUrl = "http://api.openweathermap.org/geo/1.0";
  }

  /**
   * Get simplified weather data by city name
   * @param city The name of the city
   * @returns Simplified weather data with description and location
   */
  async getWeatherByCity(city: string): Promise<SimpleWeatherResponse> {
    try {
      console.log("[OpenWeatherService] Getting weather data for city:", city);

      const url = `${this.baseUrl}/weather?q=${encodeURIComponent(city)}&appid=${this.apiKey}&units=metric`;

      // Use withRetry to handle potential failures with exponential backoff
      const response = await withRetry(
        async () => {
          console.log("[OpenWeatherService] Sending request to OpenWeather API");
          const res = await fetch(url);

          if (!res.ok) {
            const errorText = await res.text();
            throw new Error(`OpenWeather API error: ${res.status} ${errorText}`);
          }

          return res.json();
        },
        WEATHER_RETRY_CONFIG
      );

      // Extract only the needed information
      const fullResponse = response as FullWeatherResponse;
      const weatherDescription = fullResponse.weather[0]?.description || "Unknown weather";
      const locationName = `${fullResponse.name}, ${fullResponse.sys?.country || ''}`;

      const simplifiedResponse: SimpleWeatherResponse = {
        description: weatherDescription,
        location: locationName.trim()
      };

      console.log("[OpenWeatherService] Weather data retrieved successfully for city:", city);
      return simplifiedResponse;
    } catch (error) {
      console.error("[OpenWeatherService] Error getting weather data for city:", {
        city,
        error,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }

  /**
   * Get simplified weather data by geographic coordinates
   * @param lat Latitude
   * @param lon Longitude
   * @returns Simplified weather data with description and location
   */
  async getWeatherByCoordinates(lat: number, lon: number): Promise<SimpleWeatherResponse> {
    try {
      console.log("[OpenWeatherService] Getting weather data for coordinates:", { lat, lon });

      const url = `${this.baseUrl}/weather?lat=${lat}&lon=${lon}&appid=${this.apiKey}&units=metric`;

      // Use withRetry to handle potential failures with exponential backoff
      const response = await withRetry(
        async () => {
          console.log("[OpenWeatherService] Sending request to OpenWeather API");
          const res = await fetch(url);

          if (!res.ok) {
            const errorText = await res.text();
            throw new Error(`OpenWeather API error: ${res.status} ${errorText}`);
          }

          return res.json();
        },
        WEATHER_RETRY_CONFIG
      );

      // Extract only the needed information
      const fullResponse = response as FullWeatherResponse;
      const weatherDescription = fullResponse.weather[0]?.description || "Unknown weather";
      const locationName = `${fullResponse.name}, ${fullResponse.sys?.country || ''}`;

      const simplifiedResponse: SimpleWeatherResponse = {
        description: weatherDescription,
        location: locationName.trim()
      };

      console.log("[OpenWeatherService] Weather data retrieved successfully for coordinates:", { lat, lon });
      return simplifiedResponse;
    } catch (error) {
      console.error("[OpenWeatherService] Error getting weather data for coordinates:", {
        lat,
        lon,
        error,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }

  /**
   * Get city name from geographic coordinates (reverse geocoding)
   * @param lat Latitude
   * @param lon Longitude
   * @returns City location data with city name and full location string
   */
  async getCityFromCoordinates(lat: number, lon: number): Promise<CityLocationResponse> {
    try {
      console.log("[OpenWeatherService] Getting city data for coordinates:", { lat, lon });

      const url = `${this.geoBaseUrl}/reverse?lat=${lat}&lon=${lon}&limit=1&appid=${this.apiKey}`;

      // Use withRetry to handle potential failures with exponential backoff
      const response = await withRetry(
        async () => {
          console.log("[OpenWeatherService] Sending reverse geocoding request to OpenWeather API");
          const res = await fetch(url);

          if (!res.ok) {
            const errorText = await res.text();
            throw new Error(`OpenWeather API error: ${res.status} ${errorText}`);
          }

          return res.json();
        },
        WEATHER_RETRY_CONFIG
      );

      // The API returns an array of location objects, we only need the first one
      const locationData = response[0] as ReverseGeocodingResponse;

      if (!locationData) {
        throw new Error("No location data found for the provided coordinates");
      }

      const cityName = locationData.name;
      const fullLocation = locationData.state
        ? `${locationData.name}, ${locationData.state}, ${locationData.country}`
        : `${locationData.name}, ${locationData.country}`;

      const cityLocationResponse: CityLocationResponse = {
        cityName,
        fullLocation: fullLocation.trim()
      };

      console.log("[OpenWeatherService] City data retrieved successfully for coordinates:", {
        lat,
        lon,
        cityName,
        fullLocation: cityLocationResponse.fullLocation
      });

      return cityLocationResponse;
    } catch (error) {
      console.error("[OpenWeatherService] Error getting city data for coordinates:", {
        lat,
        lon,
        error,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }

  /**
   * Check if a city is valid using the OpenWeather Geocoding API
   * @param city The city name to check
   * @returns { valid: boolean, city: string, country?: string, state?: string }
   */
  async isValidCity(city: string): Promise<{ valid: boolean, city: string, country?: string, state?: string }> {
    console.log('[OpenWeatherService][isValidCity] Start', { city });
    try {
      const url = `${this.geoBaseUrl}/direct?q=${encodeURIComponent(city)}&limit=1&appid=${this.apiKey}`;
      console.log('[OpenWeatherService][isValidCity] Calling URL:', url);
      const response = await withRetry(
        async () => {
          const res = await fetch(url);
          if (!res.ok) {
            const errorText = await res.text();
            throw new Error(`OpenWeather Geocoding API error: ${res.status} ${errorText}`);
          }
          return res.json();
        },
        WEATHER_RETRY_CONFIG
      );
      console.log('[OpenWeatherService][isValidCity] Raw response:', response);
      if (Array.isArray(response) && response.length > 0) {
        const result = response[0];
        console.log('[OpenWeatherService][isValidCity] Valid city found:', result);
        return {
          valid: true,
          city: result.name,
          country: result.country,
          state: result.state
        };
      } else {
        console.log('[OpenWeatherService][isValidCity] No valid city found for:', city);
        return { valid: false, city: city };
      }
    } catch (error) {
      console.error('[OpenWeatherService][isValidCity] Error:', error);
      return { valid: false, city: city };
    } finally {
      console.log('[OpenWeatherService][isValidCity] End');
    }
  }
}
