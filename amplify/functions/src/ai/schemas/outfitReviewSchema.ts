/**
 * JSON schema for outfit review response using Gemini structured output
 * Based on the reviewOutfitPrompt.ts requirements
 */
import { Type } from "@google/genai";

export const outfitReviewSchema = {
  type: Type.OBJECT,
  properties: {
    reviewFeedback: {
      type: Type.STRING,
      description: "Comprehensive outfit review feedback as a single flowing text that includes compliments, what works, areas for improvement, quick hacks, improved version suggestions, color suggestions, accessory recommendations, additional styling tips, and a concluding quote"
    },
    suggestedUserWardrobeAlternatives: {
      type: Type.ARRAY,
      description: "Array of apparelId strings from the user's wardrobe that could improve the outfit",
      items: {
        type: Type.STRING,
        description: "Apparel ID from user's wardrobe"
      }
    },
    suggestedMarketplaceAlternatives: {
      type: Type.ARRAY,
      description: "Array of new apparel item suggestions for marketplace",
      items: {
        type: Type.OBJECT,
        properties: {
          apparelId: {
            type: Type.STRING,
            description: "Always 'new' for marketplace suggestions",
            enum: ["new"]
          },
          apparelType: {
            type: Type.STRING,
            description: "Specific type of the apparel"
          },
          apparelProfile: {
            type: Type.STRING,
            description: "Gender profile of the apparel",
            enum: ["Men", "Women"]
          },
          apparelCategory: {
            type: Type.STRING,
            description: "Category of the apparel",
            enum: ["TOPWEAR", "BOTTOMWEAR", "FOOTWEAR"]
          },
          colour: {
            type: Type.STRING,
            description: "Color of the apparel"
          },
          vibe: {
            type: Type.STRING,
            description: "Vibe of the apparel (e.g., Casual, Formal, Bohemian)"
          },
          pattern: {
            type: Type.STRING,
            description: "Pattern of the apparel (e.g., Solid, Striped, Floral)"
          },
          fabric: {
            type: Type.STRING,
            description: "Fabric or material of the apparel"
          },
          fit: {
            type: Type.STRING,
            description: "Fit of the apparel (e.g., Slim Fit, Relaxed Fit, Oversized)"
          },
          length: {
            type: Type.STRING,
            description: "Length of the apparel if applicable"
          },
          details: {
            type: Type.STRING,
            description: "Brief description or key features of the item"
          }
        },
        required: ["apparelId", "apparelType", "apparelProfile", "apparelCategory", "colour", "vibe", "pattern", "details"]
      }
    }
  },
  required: ["reviewFeedback", "suggestedUserWardrobeAlternatives", "suggestedMarketplaceAlternatives"]
};

/**
 * TypeScript interface for the outfit review response
 */
export interface OutfitReviewResponse {
  reviewFeedback: string;
  suggestedUserWardrobeAlternatives: string[];
  suggestedMarketplaceAlternatives: Array<{
    apparelId: "new";
    apparelType: string;
    apparelProfile: "Men" | "Women";
    apparelCategory: "TOPWEAR" | "BOTTOMWEAR" | "FOOTWEAR";
    colour: string;
    vibe: string;
    pattern: string;
    fabric?: string;
    fit?: string;
    length?: string;
    details: string;
  }>;
}
