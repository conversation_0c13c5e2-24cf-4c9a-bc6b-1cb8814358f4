/**
 * JSON schema for apparel processing response
 * Using the Type enum from the Gemini SDK
 */
import { Type } from "@google/genai";

export const apparelProcessingSchema = {
  type: Type.OBJECT,
  properties: {
    isValid: {
      type: Type.BOOLEAN,
      description: "Whether the image is valid for apparel processing"
    },
    errorMessage: {
      type: Type.STRING,
      description: "Error message if the image is invalid"
    },
    uniqueApparels: {
      type: Type.ARRAY,
      description: "Array of unique apparels found in the image",
      items: {
        type: Type.OBJECT,
        properties: {
          apparelCategory: {
            type: Type.STRING,
            description: "Category of the apparel (TOPWEAR, BOTTOMWEAR, FOOTWEAR)",
            enum: ["TOPWEAR", "BOTTOMWEAR", "FOOTWEAR"]
          },
          apparelProfile: {
            type: Type.STRING,
            description: "Gender profile of the apparel",
            enum: ["MALE", "FEMALE"]
          },
          apparelType: {
            type: Type.STRING,
            description: "Specific type of the apparel"
          },
          productName: {
            type: Type.STRING,
            description: "Descriptive name of the apparel"
          },
          apparelMediaUrl: {
            type: Type.STRING,
            description: "URL of the apparel image"
          },
          apparelSourceMediaUrl: {
            type: Type.STRING,
            description: "URL of the original source image"
          },
          colour: {
            type: Type.STRING,
            description: "Color of the apparel"
          },
          pattern: {
            type: Type.STRING,
            description: "Pattern of the apparel"
          },
          fit: {
            type: Type.STRING,
            description: "Fit of the apparel"
          },
          fabric: {
            type: Type.STRING,
            description: "Fabric of the apparel"
          },
          sleeves: {
            type: Type.STRING,
            description: "Sleeve length of the apparel"
          },
          sleeveType: {
            type: Type.STRING,
            description: "Type of sleeves (for female apparels)"
          },
          neckLineORCollar: {
            type: Type.STRING,
            description: "Neckline or collar type of the apparel"
          },
          shape: {
            type: Type.STRING,
            description: "Shape of the apparel"
          },
          length: {
            type: Type.STRING,
            description: "Length of the apparel"
          },
          waistRise: {
            type: Type.STRING,
            description: "Waist rise of the apparel (for bottoms)"
          },
          fade: {
            type: Type.STRING,
            description: "Fade of the apparel (for jeans)"
          },
          transparency: {
            type: Type.STRING,
            description: "Transparency of the apparel"
          },
          stretch: {
            type: Type.STRING,
            description: "Stretchability of the apparel"
          },
          ankleLength: {
            type: Type.STRING,
            description: "Ankle length of the footwear"
          },
          fastening: {
            type: Type.STRING,
            description: "Fastening type of the footwear"
          },
          toeType: {
            type: Type.STRING,
            description: "Toe type of the footwear"
          },
          heelType: {
            type: Type.STRING,
            description: "Heel type of the footwear"
          },
          insole: {
            type: Type.STRING,
            description: "Insole type of the footwear"
          },
          jacketType: {
            type: Type.STRING,
            description: "Type of jacket (for jackets)"
          }
        },
        required: ["apparelCategory", "apparelProfile", "apparelType", "productName"]
      }
    },
    duplicateDescriptions: {
      type: Type.ARRAY,
      description: "Descriptions of apparels that already exist in the user's wardrobe",
      items: {
        type: Type.STRING
      }
    }
  },
  required: ["isValid"]
};

/**
 * Interface for apparel processing response
 */
export interface ApparelProcessingResponse {
  isValid: boolean;
  errorMessage?: string;
  uniqueApparels: Array<{ //this should be apparelMetadata
    apparelCategory: "TOPWEAR" | "BOTTOMWEAR" | "FOOTWEAR";
    apparelProfile: "MALE" | "FEMALE";
    apparelType: string;
    productName: string;
    apparelMediaUrl?: string;
    apparelSourceMediaUrl?: string;
    colour?: string;
    pattern?: string;
    fit?: string;
    fabric?: string;
    sleeves?: string;
    sleeveType?: string;
    neckLineORCollar?: string;
    shape?: string;
    length?: string;
    waistRise?: string;
    fade?: string;
    transparency?: string;
    stretch?: string;
    ankleLength?: string;
    fastening?: string;
    toeType?: string;
    heelType?: string;
    insole?: string;
    jacketType?: string;
    [key: string]: any;
  }>;
  duplicateDescriptions: string[];
}
