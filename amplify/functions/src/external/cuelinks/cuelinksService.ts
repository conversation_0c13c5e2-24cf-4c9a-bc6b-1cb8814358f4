import axios from 'axios';
import { withRetry, RetryConfig } from '../../utils/retryUtil';

const CUELINKS_API_URL = 'https://www.cuelinks.com/api/v2/links.json';
const CUELINKS_API_TOKEN = process.env.CUELINKS_API_TOKEN || 'd4lUfQBr26vlUAPZIHdQao1HJS19vT0jFdSVzJV8RbQ';

// Custom retry configuration for Cuelinks API
const CUELINKS_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  backoffMultiplier: 1.5
};

interface CuelinksResponse {
  original_url: string;
  affiliate_url: string;
  shorten_url: string;
  campaign_id: number;
}

export const generateAffiliateLink = async (originalUrl: string): Promise<string> => {
  try {
    console.log(`[generateAffiliateLink] Generating affiliate link for URL: ${originalUrl}`);
    
    // Use the retry utility to handle potential transient failures
    const response = await withRetry(
      async () => {
        return axios.get<CuelinksResponse>(CUELINKS_API_URL, {
          params: {
            url: originalUrl,
            shorten: true
          },
          headers: {
            'Authorization': `Token token=${CUELINKS_API_TOKEN}`,
            'Content-Type': 'application/json'
          }
        });
      },
      CUELINKS_RETRY_CONFIG
    );

    console.log(`[generateAffiliateLink] Successfully generated affiliate link:`, {
      originalUrl,
      affiliateUrl: response.data.affiliate_url,
      shortenedUrl: response.data.shorten_url
    });

    // Return the shortened URL as it's more user-friendly
    return response.data.shorten_url;
  } catch (error) {
    console.error('[generateAffiliateLink] Error generating affiliate link:', {
      originalUrl,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}; 