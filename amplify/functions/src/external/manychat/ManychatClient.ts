import axios, { AxiosInstance } from 'axios';
import { IManychatClient } from './IManychatClient';
import {
  ManychatConfig,
  ManychatCustomField,
  ManychatCustomFields,
  ManychatResponse,
  ManychatFieldValue,
  DEFAULT_CONFIG
} from './types';
import { ManychatError } from './error';
import { withRetry } from '../../utils/retryUtil';
import { MANYCHAT_ENDPOINTS } from './constants';

/**
 * Implementation of the Manychat client
 */
export class ManychatClient implements IManychatClient {
  private readonly manychatInstance: AxiosInstance;
  private readonly config: ManychatConfig;

  constructor(config: ManychatConfig) {
    this.config = {
      ...DEFAULT_CONFIG,
      ...config
    };

    this.manychatInstance = axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: {
        'Authorization': `Bear<PERSON> ${this.config.apiKey}`,
        'Content-Type': 'application/json'
      }
    });
  }

  /**
   * Helper method to handle API errors
   */
  private handleError(error: any): ManychatError {
    if (axios.isAxiosError(error)) {
      return new ManychatError(
        error.response?.data?.message || error.message,
        error.response?.status,
        error.response?.data
      );
    }
    return new ManychatError(error.message);
  }

  /**
   * Set a single custom field for a subscriber
   */
  async setCustomField(
    subscriberId: number,
    fieldId: number,
    value: ManychatFieldValue
  ): Promise<ManychatResponse> {
    const payload: ManychatCustomField = {
      subscriber_id: subscriberId,
      field_id: fieldId,
      field_value: value
    };

    return withRetry(
      async () => {
        const response = await this.manychatInstance.post(MANYCHAT_ENDPOINTS.SET_CUSTOM_FIELD, payload);
        return response.data;
      },
      {
        maxRetries: this.config.maxRetries,
        backoffMultiplier: this.config.backoffMultiplier
      }
    ).catch(error => {
      throw this.handleError(error);
    });
  }

  /**
   * Set multiple custom fields for a subscriber
   */
  async setCustomFields(
    subscriberId: number,
    fields: Array<{ field_id: number; field_value: ManychatFieldValue }>
  ): Promise<ManychatResponse> {
    const payload: ManychatCustomFields = {
      subscriber_id: subscriberId,
      fields
    };

    console.log('Manychat setCustomFields payload:', JSON.stringify(payload, null, 2));

    return withRetry(
      async () => {
        const response = await this.manychatInstance.post(MANYCHAT_ENDPOINTS.SET_CUSTOM_FIELDS, payload);
        return response.data;
      },
      {
        maxRetries: this.config.maxRetries,
        backoffMultiplier: this.config.backoffMultiplier
      }
    ).catch(error => {
      throw this.handleError(error);
    });
  }
} 