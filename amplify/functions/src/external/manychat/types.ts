/**
 * Supported field value types in Manychat
 */
export type ManychatFieldValueType = 'text' | 'number' | 'date' | 'datetime' | 'boolean';

/**
 * Type for field values in Manychat
 */
export type ManychatFieldValue = string | number | boolean;

/**
 * Configuration interface for Manychat API
 */
export interface ManychatConfig {
  apiKey: string;
  baseURL?: string;
  timeout?: number;
  maxRetries?: number;
  backoffMultiplier?: number;
}

/**
 * Interface for a single custom field update
 */
export interface ManychatCustomField {
  subscriber_id: number;
  field_id: number;
  field_value: ManychatFieldValue;
}

/**
 * Interface for batch custom fields update
 */
export interface ManychatCustomFields {
  subscriber_id: number;
  fields: Array<{
    field_id: number;
    field_value: ManychatFieldValue;
  }>;
}

/**
 * Interface for Manychat API response
 */
export interface ManychatResponse {
  status: string;
  data?: any;
  message?: string;
}

// Default configuration
export const DEFAULT_CONFIG: Partial<ManychatConfig> = {
  baseURL: 'https://api.manychat.com/fb',
  timeout: 10000,
  maxRetries: 3,
  backoffMultiplier: 1.5
}; 