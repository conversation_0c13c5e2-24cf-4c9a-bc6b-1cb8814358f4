// apparelVibeUtil.ts
import { AIService } from "../ai/integrations/AIIntegration";
import { TypeChatService } from "../ai/integrations/TypeChatIntegration";
import {
  ApparelMetaData,
  ApparelVibe,
  MaleApparelMetaData,
  FemaleApparelMetaData,
} from "../types/apparel.types";

interface VibeResponse {
  apparelVibe: ApparelVibe;
}

export class ApparelVibeUtil {
  private aiService: AIService;
  private typeChatService: TypeChatService;

  constructor() {
    this.aiService = new AIService();
    this.typeChatService = new TypeChatService();
  }

  /**
   * Detects and assigns a vibe to apparel metadata
   * @param apparelMetaData The apparel metadata to analyze
   * @returns The updated apparel metadata with vibe
   */
  async detectAndAssignVibe(
    apparelMetaData: ApparelMetaData
  ): Promise<ApparelMetaData> {
    try {
      console.log(
        `[ApparelVibeUtil][detectAndAssignVibe] Detecting vibe for apparel type: ${apparelMetaData.apparelType}`
      );

      // Define system prompt for Gemini
      const systemPrompt = this.getSystemPrompt();

      // Get vibe analysis from Gemini
      const geminiResponse = await this.aiService.getGeminiTextAnalysis(
        JSON.stringify(apparelMetaData),
        systemPrompt,
        0.3 // Low temperature for consistent responses
      );

      // Parse response with TypeChat
      const vibeResult = await this.parseVibeResponse(geminiResponse);

      // Update metadata with the detected vibe
      const updatedMetadata = {
        ...apparelMetaData,
        vibe: vibeResult.apparelVibe,
      };

      console.log(
        `[ApparelVibeUtil][detectAndAssignVibe] Detected vibe: ${vibeResult.apparelVibe} for apparel type: ${apparelMetaData.apparelType}`
      );

      return updatedMetadata;
    } catch (error) {
      console.error(
        `[ApparelVibeUtil][detectAndAssignVibe] Error detecting vibe: ${error}`
      );
      // Return original metadata if vibe detection fails
      return apparelMetaData;
    }
  }

  /**
   * Parses the Gemini response to extract the vibe
   * @param response The raw text response from Gemini
   * @returns The parsed vibe response
   */
  private async parseVibeResponse(response: string): Promise<VibeResponse> {
    try {
      // Leverage TypeChatService to parse response
      return await this.typeChatService.parseVibeResponse(response);
    } catch (error) {
      console.error(
        `[ApparelVibeUtil][parseVibeResponse] Error parsing vibe: ${error}`
      );
      // Default to 'casual' if parsing fails
      return { apparelVibe: "casual" };
    }
  }
  /**
   * Gets the system prompt for Gemini
   * @returns The system prompt string
   */
  private getSystemPrompt(): string {
    return `
You are a fashion expert AI tasked with analyzing apparel descriptions to determine their fashion vibe.
Based on the apparel description, you must select ONE of the following vibes that best matches:
- bold: Makes a statement, eye-catching, distinctive, confident
- playful: Fun, whimsical, cheerful, expressive, youthful
- casual: Relaxed, everyday wear, comfortable, laid-back
- edgy: Rebellious, avant-garde, unconventional, boundary-pushing
- minimal: Clean lines, understated, simple, modern
- ethnic: Traditional designs, cultural elements, heritage-inspired
- bohemian: Free-spirited, artistic, eclectic, nature-inspired
- sporty: Athletic, performance-oriented, dynamic, functional
- elegant: Refined, sophisticated, graceful, timeless
- professional: Polished, businesslike, structured, formal

You must analyze the apparel characteristics (type, color, pattern, fabric, etc.) and respond ONLY with a JSON object in this exact format:
{
  "apparelVibe": "[ONE_VIBE_FROM_LIST]"
}

Do not include any explanation, additional text, or multiple vibes. ONLY return the JSON object with the single most appropriate vibe selected from the provided list.
`;
  }
}
