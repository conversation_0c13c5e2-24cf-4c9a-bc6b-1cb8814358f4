import { AIService } from "../ai/integrations/AIIntegration";
import {
  ApparelProfile,
  UserWardrobeInfo
} from "../types/apparel.types";
import {
  APPAREL_PROCESSING_SYSTEM_PROMPT
} from "../ai/prompts/apparelProcessingPrompt";
import {
  ApparelProcessingResponse,
  apparelProcessingSchema
} from "../ai/schemas/apparelProcessingSchema";

/**
 * Utility class for processing apparel images
 */
export class ApparelProcessingUtil {
  private aiService: AIService;

  constructor() {
    this.aiService = new AIService();
  }

  /**
   * Combined method to validate image, detect duplicates, and tag apparels in a single call
   * @param imageUrl The URL of the image to process
   * @param existingApparels The user's existing apparels
   * @param gender The user's gender
   * @returns Result with validation status, unique apparel metadata, and duplicate descriptions
   */
  async processApparelImage(
    imageUrl: string,
    existingApparels: UserWardrobeInfo[],
    gender: ApparelProfile
  ): Promise<ApparelProcessingResponse> {
    try {
      console.log(`[ApparelProcessingUtil] Processing image with ${existingApparels.length} existing apparels`);

      // Create a simplified representation of existing apparels for comparison
      const existingApparelsJson = JSON.stringify(existingApparels.map(item => ({
        apparelType: item.apparel.apparelType,
        apparelCategory: item.apparel.apparelCategory,
        colour: item.apparel.colour,
        pattern: item.apparel.pattern,
        description: item.apparel.productName
      })));

      // Get the system prompt with the existing apparels and gender
      const systemPrompt = APPAREL_PROCESSING_SYSTEM_PROMPT(gender, existingApparelsJson);

      // Use the structured output method to get a properly typed response directly
      const result = await this.aiService.getGeminiImageStructuredOutput<ApparelProcessingResponse>(
        imageUrl,
        "",  // Empty user prompt to avoid duplication
        systemPrompt,
        apparelProcessingSchema
      );

      console.log(`[ApparelProcessingUtil] Found ${result.uniqueApparels?.length || 0} unique apparels and ${result.duplicateDescriptions?.length || 0} duplicates`);

      // Ensure the apparelProfile and source media URL are set correctly for each apparel
      if (result.uniqueApparels) {
        result.uniqueApparels.forEach(apparel => {
          apparel.apparelProfile = gender;
          apparel.apparelSourceMediaUrl = imageUrl;
        });
      }

      return {
        isValid: result.isValid,
        errorMessage: result.errorMessage,
        uniqueApparels: result.uniqueApparels || [],
        duplicateDescriptions: result.duplicateDescriptions || []
      };
    } catch (error) {
      console.error(`[ApparelProcessingUtil] Error processing image:`, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return {
        isValid: false,
        errorMessage: error instanceof Error ? error.message : 'Error processing image',
        uniqueApparels: [],
        duplicateDescriptions: []
      };
    }
  }
}
