import { AIService } from '../ai/integrations/AIIntegration';
import { OUTFIT_EMOJIFICATION_PROMPT } from '../ai/prompts/outfitEmojificationPrompt';
import { CITY_EXTRACTION_SCHEMA } from '../ai/schemas/cityExtractionSchema';

export class AIUtil {
  private aiService: AIService;

  constructor() {
    this.aiService = new AIService();
  }

  /**
   * Converts an outfit description into an emojified, human-readable format
   * @param occasion The occasion for the outfit
   * @param outfit The outfit description to emojify
   * @returns A formatted, emojified outfit description
   */
  async emojifyOutfit(occasion: string, outfit: string): Promise<string> {
    try {
      const userPrompt = `Occasion is ${occasion}, outfit is ${outfit}`;
      const response = await this.aiService.getAIResponse(
        [userPrompt],
        OUTFIT_EMOJIFICATION_PROMPT
      );
      return response;
    } catch (error) {
      console.error('[AIUtil] Error emojifying outfit:', error);
      throw error;
    }
  }

  /**
   * Extract up to 3 possible city names from user input using Gemini (structured output)
   * @param userInput The user input string
   * @returns An object with cities (string[]) and a user-friendly message
   */
  async getCityCandidatesFromText(userInput: string): Promise<{ cities: string[], message: string }> {
    console.log('[AIUtil][getCityCandidatesFromText] Start', { userInput });
    try {
      const { textToCityPrompt } = await import('../ai/prompts/textToCityPrompt');
      const systemPrompt = textToCityPrompt;
      const prompt = userInput;
      const result = await this.aiService.getGeminiTextAnalysisStructuredOutput<{ cities: string[], message: string }>(
        prompt,
        systemPrompt,
        CITY_EXTRACTION_SCHEMA,
        0.3
      );
      console.log('[AIUtil][getCityCandidatesFromText] Structured result:', result);
      return result;
    } catch (error) {
      console.error('[AIUtil][getCityCandidatesFromText] Error:', error);
      return { cities: [], message: "Sorry, something went wrong while extracting city names." };
    } finally {
      console.log('[AIUtil][getCityCandidatesFromText] End');
    }
  }
} 