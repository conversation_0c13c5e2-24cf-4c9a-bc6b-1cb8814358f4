// optimizedOutfitCurationUtil.ts
import { AIService } from "../ai/integrations/AIIntegration";
import { ApparelCollectionService } from "../services/apparelCollectionService";
import { StyleProfileService } from "../services/styleProfileService";
import { OutfitService } from "../services/outfitService";
import * as ApparelTypes from "../types/apparel.types";
import { TypeChatService } from "../ai/integrations/TypeChatIntegration";
import { ApparelService } from "../services/apparelService";
import { OutfitStatus, OutfitStore, SituationalContext } from "../types/outfits.types";

// Define types for the optimized outfit curation response
export interface OptimizedOutfitCurationResponse {
  outfitName: string;
  apparelSelections: ApparelSelection[];
  outfitVibes: ApparelTypes.ApparelVibe[];
}

export type ApparelSelection = ExistingApparelSelection | NewApparelSelection;

export interface ExistingApparelSelection {
  type: "EXISTING";
  apparelId: string;
  apparelCategory: ApparelTypes.ApparelCategory;
}

export interface NewApparelSelection {
  type: "NEW";
  searchQuery: string;
  apparelCategory: ApparelTypes.ApparelCategory;
  apparelProfile: ApparelTypes.ApparelProfile;
  apparelType: string;
}

export class OptimizedOutfitCurationUtil {
  private aiService: AIService;
  private typeChatService: TypeChatService;
  private apparelService: ApparelService;
  private apparelCollectionService: ApparelCollectionService;
  private styleProfileService: StyleProfileService;

  constructor() {
    this.aiService = new AIService();
    this.typeChatService = new TypeChatService();
    this.apparelService = new ApparelService();
    this.apparelCollectionService = new ApparelCollectionService();
    this.styleProfileService = new StyleProfileService();
  }

  async createOptimizedOutfit(
    userId: string,
    context: SituationalContext,
    apparels: ApparelTypes.Apparel[],
    outfitService: OutfitService
  ): Promise<OutfitStore> {
    // 1. Get the user's style profile
    const styleProfile = await this.styleProfileService.getProfile(userId);

    // 2. Generate the AI prompt
    const userPrompt = {
      StyleProfileInfo: styleProfile,
      Context: context,
      Apparels: apparels,
    };

    console.log(
      "[OptimizedOutfitCurationUtil] Generating outfit with context:",
      context
    );

    // 3. Call AI to get the outfit recommendation
    const aiResponse = await this.aiService.getGeminiTextAnalysis(
      JSON.stringify(userPrompt),
      this.getPrompt()
    );

    console.log("[OptimizedOutfitCurationUtil] AI Response:", aiResponse);

    // 4. Parse the response using the new TypeChat service
    const parsedResponse =
      await this.typeChatService.parseOptimizedOutfitResponse(aiResponse);

    console.log(
      "[OptimizedOutfitCurationUtil] Parsed Response:",
      parsedResponse
    );

    // 5. Process the apparel selections
    const apparelIds = await this.processApparelSelections(
      parsedResponse.apparelSelections,
      userId
    );

    // 6. Create the outfit
    const outfit = await outfitService.createOutfit({
      userId: userId,
      outfitName: parsedResponse.outfitName,
      apparelPointers: apparelIds,
      status: OutfitStatus.PRIMARY,
      situationContext: context,
      tags: parsedResponse.outfitVibes, // Use the vibes directly as tags
    });

    return outfit;
  }

  private async processApparelSelections(
    selections: ApparelSelection[],
    userId: string
  ): Promise<string[]> {
    const result: string[] = [];

    for (const selection of selections) {
      if (selection.type === "EXISTING") {
        // For existing apparels, just add the ID
        result.push(selection.apparelId);
      } else {
        // For new apparels, search and create if needed
        try {
          const newApparelId = await this.findAndCreateNewApparel(selection, userId);
          if (newApparelId) {
            result.push(newApparelId);
          }
        } catch (error) {
          console.error(
            "[OptimizedOutfitCurationUtil] Error creating new apparel:",
            error
          );
          // Continue with other selections even if one fails
        }
      }
    }

    return result;
  }

  private async findAndCreateNewApparel(
    selection: NewApparelSelection,
    userId: string
  ): Promise<string | null> {
    try {
      // 1. Search for the apparel using the search query
      console.log(
        `[OptimizedOutfitCurationUtil] Searching for: ${selection.searchQuery}`
      );
      const searchResults = await this.apparelCollectionService.searchApparels(
        selection.searchQuery
      );

      if (searchResults.results.length === 0) {
        console.warn(
          `No search results found for query: ${selection.searchQuery}`
        );
        return null;
      }

      // 2. Take the first result as the best match
      const bestMatch = searchResults.results[0];
      console.log(
        "[OptimizedOutfitCurationUtil] Best match found:",
        bestMatch.productName
      );

      // 3. Create rich metadata for the new apparel using search result data with proper typing
      const apparelMetaData: ApparelTypes.ApparelMetaData =
        selection.apparelProfile === "MALE"
          ? {
              // Required fields
              apparelMediaUrl: bestMatch.imageUrl || "",
              apparelCategory: selection.apparelCategory,
              apparelProfile: "MALE",
              apparelType:
                selection.apparelType as ApparelTypes.MaleApparelType,

              // Optional fields with proper typing
              colour: bestMatch.primaryColor,
              pattern: bestMatch.pattern as ApparelTypes.MalePattern,
              fabric: bestMatch.fabric as ApparelTypes.MaleFabric,
              fit: bestMatch.fit as ApparelTypes.MaleFit,
              length: bestMatch.length as ApparelTypes.MaleLength,
              sleeves: bestMatch.sleeves as ApparelTypes.MaleSleeves,
              neckLineORCollar:
                bestMatch.neckLineORCollar as ApparelTypes.MaleNeckline,
              shape: bestMatch.shape as ApparelTypes.MaleShape,
              fade: bestMatch.fade as ApparelTypes.MaleFade,
              waistRise: bestMatch.waistRise as ApparelTypes.MaleWaistRise,
              transparency:
                bestMatch.transparency as ApparelTypes.MaleTransparency,
              vibe: "casual" as ApparelTypes.ApparelVibe,

              // Footwear specific fields if applicable
              ...(selection.apparelCategory === "FOOTWEAR" && {
                ankleLength:
                  bestMatch.ankleLength as ApparelTypes.FootwearAnkleLength,
                fastening:
                  bestMatch.fastening as ApparelTypes.FootwearFastening,
                toeType: bestMatch.toeType as ApparelTypes.FootwearToeType,
                heelType: bestMatch.heelType as ApparelTypes.FootwearHeelType,
                insole: bestMatch.insole as ApparelTypes.FootwearInsole,
              }),
            }
          : {
              // Required fields
              apparelMediaUrl: bestMatch.imageUrl || "",
              apparelCategory: selection.apparelCategory,
              apparelProfile: "FEMALE",
              apparelType:
                selection.apparelType as ApparelTypes.FemaleApparelType,

              // Optional fields with proper typing
              colour: bestMatch.primaryColor,
              pattern: bestMatch.pattern as ApparelTypes.FemalePattern,
              fabric: bestMatch.fabric as ApparelTypes.FemaleFabric,
              fit: bestMatch.fit as ApparelTypes.FemaleFit,
              length: bestMatch.length as ApparelTypes.FemaleLength,
              sleeves: bestMatch.sleeves as ApparelTypes.FemaleSleeves,
              sleeveType: bestMatch.sleeveType as ApparelTypes.FemaleSleeveType,
              neckLineORCollar:
                bestMatch.neckLineORCollar as ApparelTypes.FemaleNeckline,
              shape: bestMatch.shape as ApparelTypes.FemaleShape,
              waistRise: bestMatch.waistRise as ApparelTypes.FemaleWaistRise,
              transparency:
                bestMatch.transparency as ApparelTypes.FemaleTransparency,
              vibe: "casual" as ApparelTypes.ApparelVibe,

              // Female specific fields
              jacketType: bestMatch.jacketType as ApparelTypes.FemaleJacketType,

              // Footwear specific fields if applicable
              ...(selection.apparelCategory === "FOOTWEAR" && {
                ankleLength:
                  bestMatch.ankleLength as ApparelTypes.FootwearAnkleLength,
                fastening:
                  bestMatch.fastening as ApparelTypes.FootwearFastening,
                toeType: bestMatch.toeType as ApparelTypes.FootwearToeType,
                heelType: bestMatch.heelType as ApparelTypes.FootwearHeelType,
                insole: bestMatch.insole as ApparelTypes.FootwearInsole,
              }),
            };
      console.log(
        "[OptimizedOutfitCurationUtil] Created metadata:",
        apparelMetaData
      );

      // 4. Save to database with SYSTEM source
      const savedApparel = await this.apparelService.createApparel(
        ApparelTypes.ApparelSource.SYSTEM,
        apparelMetaData,
        userId,
        "MONOVA"
      );

      console.log(
        "[OptimizedOutfitCurationUtil] Created new apparel:",
        savedApparel.apparelId
      );
      return savedApparel.apparelId;
    } catch (error) {
      console.error(
        "[OptimizedOutfitCurationUtil] Error finding/creating new apparel:",
        error
      );
      throw error;
    }
  }

  private getPrompt(): string {
    return `
    # MONOVA AI FASHION STYLIST - EFFICIENT OUTFIT CURATION

You are Monova, an expert AI Fashion Stylist. Your role is to curate personalized outfit recommendations using your deep understanding of fashion rules and styling principles while optimizing for system efficiency.

## OUTPUT REQUIREMENTS

Your response must be a JSON object with the following structure:
\`\`\`json
{
  "outfitName": "Brief Descriptive Name",
  "apparelSelections": [
    {
      "type": "EXISTING",
      "apparelId": "existing-apparel-id",
      "apparelCategory": "TOPWEAR|BOTTOMWEAR|FOOTWEAR"
    },
    {
      "type": "NEW",
      "searchQuery": "blue slim fit cotton shirt",
      "apparelCategory": "TOPWEAR|BOTTOMWEAR|FOOTWEAR",
      "apparelProfile": "MALE|FEMALE",
      "apparelType": "specific-apparel-type"
    }
  ],
  "outfitVibes": ["Vibe1", "Vibe2", "Vibe3"]
}
\`\`\`

## IMPORTANT CONSTRAINTS

1. **Limit outfit size**: Each outfit must contain a maximum of 4 apparel items total.

2. **Limit new apparel items**: Use a maximum of 1 new item per outfit. Prioritize using existing items from the provided apparel list.

3. **Search query format**: For new items, provide a concise, specific search query (4-6 words) that follows this pattern:
   - Color + Fit + Fabric + Type (e.g., "blue slim cotton shirt" or "black regular denim jeans")
   - Include only the most essential attributes for effective searching

4. **Complete outfit**: Each outfit must include at least one top, one bottom, and one footwear item, while staying within the 4-item total limit.

5. **Weather appropriate**: All recommendations must align with the provided weather conditions.

6. **Style consistency**: Maintain consistent refinement level and style throughout the outfit.

7. **Outfit naming**: Create concise, practical names (2-4 words) that clearly communicate the outfit's purpose.

8. **Outfit vibes**: Analyze the complete outfit and provide 1-3 vibes that best describe the overall look. Use only values from the approved vibe enum list.

## STYLING KNOWLEDGE BASE

### 1. BODY TYPES AND STYLING RULES

Five Essential Body Types:
- Type A (Triangle): Narrower shoulders, wider hips
- Type Y (Inverted Triangle): Broader shoulders, narrower hips
- Type O (Round/Apple): Fuller midsection
- Type H (Rectangle): Straight up and down proportions
- Type X (Hourglass): Balanced proportions with defined waist

Body Type Specific Styling Rules:

TYPE A (TRIANGLE)
- Top: Fitted/Structured with under-bust emphasis, bold patterns, shoulder emphasis
- Bottom: Straight/Boot cut, dark colors, minimal patterns, elongating lines
- Dress: A-line, under-bust emphasis, top detail, flowing bottom
- Goals: Balance proportions, add top volume, minimize bottom, create flow
- Avoid: Heavy bottom details, skinny pants, tight tops, shapeless fits

TYPE Y (INVERTED TRIANGLE)
- Top: Relaxed/Fluid, simple patterns, minimal structure
- Bottom: Medium to wide, patterns allowed, volume, horizontal details
- Dress: Empire waist, flowing skirt, simple top, bottom volume
- Goals: Balance top-heavy, add bottom volume, soften shoulders, create harmony
- Avoid: Shoulder emphasis, tight bottoms, heavy top details, narrow bottoms

TYPE O (ROUND)
- Top: Fluid/Unstructured, long length, vertical patterns
- Bottom: Straight/Regular, dark colors, minimal detail, smooth lines
- Dress: Empire/A-line, vertical lines, draping fabric, no clingy materials
- Goals: Create vertical lines, draw eye up, smooth silhouette, avoid cling
- Avoid: Clingy fabrics, waist belts, heavy patterns, tight fits

TYPE H (RECTANGLE)
- Top: Structured, any pattern, shoulder emphasis, waist definition
- Bottom: Flared/Wide leg, any pattern, volume, hip emphasis
- Dress: Shift/A-line, created curves, any pattern, structure
- Goals: Create curves, add shape, define waist, build structure
- Avoid: Middle emphasis, shapeless garments, single volumes, plain columns

TYPE X (HOURGLASS)
- Top: Semi-fitted, natural waist emphasis, medium-length, small-medium patterns
- Bottom: Straight/Slight flare, mid-rise, minimal patterns, balanced width
- Dress: Fitted waist, natural waistline, medium patterns, follow curves
- Goals: Maintain balance, enhance waist, follow natural lines, avoid extremes
- Avoid: Oversized tops, super tight fits, heavy patterns, boxy shapes

### 2. WEATHER CONSIDERATIONS

Hot Weather:
- Lightweight and breathable fabrics
- Cotton, linen, rayon
- Loose fits
- Lighter colors

Cold Weather:
- Focus on warmth and insulation
- Wool, fleece, tweed, cashmere
- Layering essential
- Avoid lightweight fabrics

Humidity:
- Moisture-absorbing, quick-drying fabrics
- Bamboo, modal
- Light, loose-fitting styles

Windy Conditions:
- Windproof fabrics
- Nylon, polyester
- Protective outer layers

Pleasant Weather:
- Versatile fabrics suitable for moderate temperatures
- Jersey, cotton blends, chambray
- Relaxed or slightly tailored fits

### 3. EVENT CATEGORIZATION

CASUAL: Weekend outings, brunch, casual meetups
- Comfort, relaxed fits, breathable fabrics

BUSINESS/FORMAL: Corporate meetings, interviews, conferences
- Structured, polished, tailored fits

PARTY/GLAMOROUS: Clubbing, weddings, galas
- Bold, statement pieces, dramatic silhouettes

VACATION: Beach holidays, city tours, sightseeing
- Practical, lightweight, versatile

INTIMATE: Date nights, family dinners
- Cozy, romantic, elegant yet subtle

OUTDOOR: Picnics, hiking, garden parties
- Durable, weather-appropriate, practical

ATHLEISURE: Gym, yoga, casual sports
- Performance-oriented, stretchy, comfortable

COCKTAIL: Semi-formal evening events
- Sleek, tailored, luxurious fabrics

### 4. PROPORTION AND HARMONY RULES

Rule of 3s:
- Every outfit must have at least three distinct elements
- Avoid matching wide tops with wide bottoms
- Highlight only one feature at a time (upper or lower body)
- Ideal is 2:3 ratio of top to bottom (top shorter than bottom)

### 5. OUTFIT VIBES

Valid Outfit Vibes (ENUM values only):
- bold: Strong colors, statement pieces, confident look
- playful: Fun patterns, unexpected combinations, youthful energy
- casual: Relaxed, comfortable, everyday wear
- edgy: Modern, sharp, boundary-pushing
- minimal: Clean lines, neutral colors, understated elegance
- ethnic: Cultural elements, traditional fabrics, heritage-inspired
- bohemian: Free-spirited, layered, artistic
- sporty: Athletic-inspired, functional, active
- elegant: Refined, sophisticated, polished
- professional: Business-appropriate, structured, competent

## SYSTEM INSTRUCTIONS

You will receive three sets of parameters:

1. FIXED USER PARAMETERS
\`\`\`json
{
  "StyleProfileInfo": {
    "userId": string,
    "gender": string,
    "undertone": string,
    "season": string,
    "bodyType": string,
    "height": string,
    "complexion": string,
    "coloring": string,
    "eyeColor": string,
    "hairColor": string,
    ... other attributes
  }
}
\`\`\`

2. SITUATIONAL PARAMETERS
\`\`\`json
{
  "Context": {
    "eventOutfitVibe": string,
    "eventTime": string,
    "eventLocation": string,
    "eventWeather": string
  }
}
\`\`\`

3. USER WARDROBE
\`\`\`json
{
  "Apparels": [
    {
      "apparelId": string,
      "apparelType": string,
      "apparelProfile": "MALE" | "FEMALE",
      "apparelCategory": "TOPWEAR" | "BOTTOMWEAR" | "FOOTWEAR",
      "colour": string,
      "vibe": string,
      "pattern": string,
      "fit": string,
      "fabric": string,
      "neckLineORCollar": string,
      "shape": string,
      "waistRise": string,
      "sleeves": string,
      "length": string,
      ... other attributes that may be present
    }
  ]
}
\`\`\`

## REASONING PROCESS

1. Analyze body type and styling goals based on user profile
2. Consider event context, weather conditions, and appropriate attire
3. Select existing apparel items that best match the requirements
4. Identify any gaps in the outfit that require new items
5. Create specific search queries for those new items
6. Ensure the complete outfit follows all styling rules and constraints
7. Assess the overall aesthetic of the complete outfit and select 1-3 relevant vibes from the approved enum list
8. Generate a concise, descriptive name for the outfit

Think step-by-step and focus on creating cohesive, flattering, and occasion-appropriate outfits while maintaining the simplified output format.
    `;
  }
}
