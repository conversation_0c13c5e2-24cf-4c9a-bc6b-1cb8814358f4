import { AIService } from "../ai/integrations/AIIntegration";
import { TypeChatService } from "../ai/integrations/TypeChatIntegration";
import { Contrast, SelfieAnalysisResponse, Season } from "../types/style.types";
import { Undertone } from "../types/style.types";
import { SELFIE_ANALYSIS_PROMPT } from "../ai/prompts/selfieAnalysisPrompt";

export class ImageAnalysisUtil {
  private aiService: AIService;
  private typeChatService: TypeChatService;

  constructor() {
    this.aiService = new AIService();
    this.typeChatService = new TypeChatService();
  }

  async analyzeUserSelfie(
    imageUrl: string,
    userUndertone: Undertone
  ): Promise<SelfieAnalysisResponse> {

    try {
      const aiResponse = await this.aiService.getGeminiImageAnalysis(
        imageUrl,
        "Analyze this image for facial features and coloring.",
        SELFIE_ANALYSIS_PROMPT
      );

      console.log("[ImageAnalysisUtil] AI response:", aiResponse);

      const validatedResponse =
        await this.typeChatService.parseImageAnalysisResponse(
          JSON.stringify(aiResponse)
        );

      console.log("[ImageAnalysisUtil] Validated response:", validatedResponse);

      //TODO : Ideally this should not be allowed since we are giving false info
      if (!validatedResponse.isValid) {
        return { ...validatedResponse, userSeason: "SUMMER" };
      }

      const userSeason = this.determineSeason(
        userUndertone,
        validatedResponse.userContrast ?? (() => { throw new Error("Missing required userContrast in validated response") })()
      );

      return { ...validatedResponse, userSeason };
      // Remove the isValidImage field before returning
    } catch (error) {
      console.error("[ImageAnalysisUtil] Error analyzing image:", {
        error,
        imageUrl: imageUrl.substring(0, 30) + "...",
      });
      throw error;
    }
  }

  determineSeason(undertone: Undertone, contrast: Contrast): Season {
    // Convert undertone and contrast to uppercase to ensure they match the matrix keys
    const normalizedUndertone = undertone.toUpperCase() as Undertone;
    const normalizedContrast = contrast.toUpperCase() as Contrast;

    const seasonMatrix: Record<Undertone, Record<Contrast, Season>> = {
      COOL: {
        LIGHT: "SUMMER",
        DARK: "WINTER",
      },
      WARM: {
        LIGHT: "SPRING",
        DARK: "AUTUMN",
      },
    };

    return seasonMatrix[normalizedUndertone][normalizedContrast];
  }
}
