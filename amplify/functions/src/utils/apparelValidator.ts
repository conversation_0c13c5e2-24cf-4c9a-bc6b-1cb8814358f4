import { 
  ApparelMetaData, 
  MaleApparelMetaData, 
  FemaleApparelMetaData,
  ApparelProfile
} from "../types/apparel.types";

export class ApparelValidator {
  // Utility type to get required fields (fields without '?' or 'undefined')
  private static getRequiredFields<T>(obj: T): (keyof T)[] {
    return Object.entries(obj as Record<string, unknown>)
      .filter(([_, value]) => value !== undefined)
      .map(([key, _]) => key as keyof T);
  }

  static validateCreateApparelMetadata(metadata: ApparelMetaData): void {
    this.validateRequiredFields(metadata);

    // Profile-specific validation
    if (metadata.apparelProfile === "MALE") {
      this.validateRequiredFields(metadata as MaleApparelMetaData);
    } else if (metadata.apparelProfile === "FEMALE") {
      this.validateRequiredFields(metadata as FemaleApparelMetaData);
    } else {
      throw new Error(`Invalid apparelProfile: must be either "MALE" or "FEMALE"`);
    }
  }

  private static validateRequiredFields<T>(metadata: T): void {
    // Get required fields from the type itself
    type RequiredFields = {
      [K in keyof T]-?: T[K] extends undefined ? never : K
    }[keyof T];

    const requiredFields = this.getRequiredFields(metadata);
    const missingFields = requiredFields.filter(field => !metadata[field]);

    if (missingFields.length > 0) {
      throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
    }
  }
}
