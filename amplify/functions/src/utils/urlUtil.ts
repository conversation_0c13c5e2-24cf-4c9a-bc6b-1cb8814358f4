/**
 * Utility functions for URL operations
 */

/**
 * Validates if a string is a valid URL
 * @param url The string to validate as a URL
 * @returns An object with valid flag and optional error message
 */
export const isValidUrl = (url: string): { valid: boolean; error?: string } => {
  try {
    // Check if the string is empty or undefined
    if (!url || url.trim() === '') {
      return { valid: false, error: 'URL is empty or undefined' };
    }

    // Check if the URL starts with http:// or https://
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return { valid: false, error: 'URL must start with http:// or https://' };
    }

    // Try to create a URL object - this will throw if the URL is invalid
    new URL(url);

    return { valid: true };
  } catch (error) {
    return { 
      valid: false, 
      error: error instanceof Error ? error.message : 'Invalid URL format' 
    };
  }
};
