/**
 * Utility for handling retries with exponential backoff
 */

/**
 * Configuration options for retry behavior
 */
export interface RetryConfig {
  maxRetries?: number;
  backoffMultiplier?: number;
}

/**
 * Default retry configuration
 */
export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  backoffMultiplier: 1.5
};

/**
 * Executes an operation with retry logic using exponential backoff
 * 
 * @param operation - The async operation to retry
 * @param config - Retry configuration options
 * @param retryCount - Current retry count (internal use)
 * @returns Promise with the operation result
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  config: RetryConfig = DEFAULT_RETRY_CONFIG,
  retryCount: number = 0
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    if (retryCount >= (config.maxRetries || 0)) {
      throw error;
    }

    // Calculate delay with exponential backoff
    const delay = Math.pow(config.backoffMultiplier || 1.5, retryCount) * 1000;
    await new Promise(resolve => setTimeout(resolve, delay));

    return withRetry(operation, config, retryCount + 1);
  }
} 