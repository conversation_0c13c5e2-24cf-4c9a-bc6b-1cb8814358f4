// Base type definitions
export enum ApparelSource {
  USER = "USER",
  SYSTEM = "SYSTEM",
  VENDOR = "VENDOR",
  MYNTRA = "MYNTRA"
}

export enum ApparelStatus {
  PRIMARY = "PRIMARY",
  WISHLIST = "WISHLIST",
  ARCHIVED = "ARCHIVED",
}

export type ApparelProfile = "MALE" | "FEMALE";
export type ApparelCategory = "TOPWEAR" | "BOTTOMWEAR" | "FOOTWEAR";

// Common base interface with shared fields
export interface BaseApparelMetaData {
  apparelMediaUrl: string;
  apparelSourceMediaUrl?: string; // URL of the original image from which the apparel was extracted
  apparelId?: string; // Added apparelId to BaseApparelMetaData
  vibe?: ApparelVibe;
  forOccasion?: string;
  productName?: string;
  brand?: string;
}

export type ApparelVibe =
  | "bold"
  | "playful"
  | "casual"
  | "edgy"
  | "minimal"
  | "ethnic"
  | "bohemian"
  | "sporty"
  | "elegant"
  | "professional";

// Male-specific types
export type MaleApparelType =
  | "tshirts"
  | "shirts"
  | "casual_shirt"
  | "formal_shirt"
  | "sweatshirts"
  | "sweaters"
  | "jacket"
  | "blazers"
  | "suit"
  | "jeans"
  | "casual_trouser"
  | "formal_trouser"
  | "trousers"
  | "shorts"
  | "track_pants"
  | "kurtas"
  | "waistcoat"
  | "rain_jacket"
  | "nehru_jackets"
  | "jackets"
  | "jogger"
  // Add footwear types
  | "casual_shoes"
  | "flats"
  | "sports_shoes"
  | "formal_shoes"
  | "boat_shoes"
  | "brogues"
  | "clogs"
  | "derbys"
  | "driving_shoes"
  | "espadrilles"
  | "flatforms"
  | "loafers"
  | "mojaris"
  | "monks"
  | "mule_sneakers"
  | "mules"
  | "oxfords"
  | "skate_shoes"
  | "slip_on_shoes"
  | "slip_on_sneakers"
  | "sneakers"
  | "trekking_shoes"
  | "biker_boots"
  | "chelsea_boots"
  | "chunky_boots"
  | "cowboy_boots"
  | "desert_boots"
  | "hiking_boots"
  | "monk_straps"
  | "rain_boots"
  | "regular_boots"
  | "sliders"
  | "winter_boots";

export type MalePattern =
  | "geometric"
  | "geometric_printed"
  | "floral"
  | "floral_printed"
  | "solid"
  | "ethnic_motifs"
  | "striped"
  | "woven_design"
  | "polka_dots"
  | "chevron"
  | "abstract"
  | "abstract_printed"
  | "checked"
  | "quirky"
  | "leheriya"
  | "paisley"
  | "embellished"
  | "colourblocked"
  | "bandhani"
  | "tribal"
  | "animal"
  | "animal_printed"
  | "tie_and_dye"
  | "graphic"
  | "conversational"
  | "brand_logo"
  | "brand_logo_printed"
  | "typography"
  | "washed"
  | "camouflage"
  | "self_design"
  | "ombre"
  | "houndstooth"
  | "superhero"
  | "textured_self_design"
  | "solid_checked"
  | "solid_self_design";

export type MaleFit =
  | "slim_fit"
  | "regular_fit"
  | "relaxed_fit"
  | "straight_fit"
  | "loose_fit"
  | "tapered_fit"
  | "skinny_fit"
  | "super_skinny_fit"
  | "bootcut"
  | "baggy"
  | "jogger"
  | "flared"
  | "oversized"
  | "tailored_fit"
  | "boxy"
  | "compression"
  | "muscle_fit"
  | "wide_leg"
  | "slouchy_fit"
  | "mom_fit"
  | "dad_fit"
  | "boyfriend_fit";

export type MaleFabric =
  | "modal"
  | "cotton"
  | "cotton_linen"
  | "pure_cotton"
  | "blended"
  | "polyester"
  | "viscose_rayon"
  | "lyocell"
  | "organic_cotton"
  | "jacquard"
  | "linen"
  | "linen_blend"
  | "polycotton"
  | "silk"
  | "cotton_silk"
  | "raw_silk"
  | "nylon"
  | "denim"
  | "hemp"
  | "khadi"
  | "acrylic"
  | "velvet"
  | "wool"
  | "fleece"
  | "leather"
  | "suede"
  | "synthetic"
  | "satin"
  | "elastane"
  | "crepe"
  | "corduroy"
  | "liva"
  | "pu"
  | "tencel"
  | "bamboo"
  | "poly_silk"
  | "textile"
  | "chanderi_silk"
  | "cashmere"
  | "cotton_wool"
  | "cupro"
  | "georgette";

export type MaleSleeves =
  | "long"
  | "short"
  | "sleeveless"
  | "three_quarter";

export type MaleCollar =
  | "spread_collar"
  | "button_down_collar"
  | "mandarin_collar"
  | "band_collar"
  | "hood"
  | "cuban_collar"
  | "slim_collar"
  | "cutaway_collar"
  | "club_collar"
  | "collarless"
  | "peter_pan_collar"
  | "wingtip_collar"
  | "built_up_collar";

export type MaleNeckline =
  | "round_neck"
  | "shirt_collar"
  | "mandarin_collar"
  | "band_collar"
  | "stylised_neck"
  | "v_neck"
  | "keyhole_neck"
  | "boat_neck"
  | "square_neck"
  | "halter_neck"
  | "turtle_neck"
  | "mock_collar"
  | "shawl_collar"
  | "lapel_collar"
  | "high_neck"
  | "hood"
  | "henley_neck"
  | "peter_pan_collar"
  | "scoop_neck"
  | "shawl_neck"
  | "cowl_neck"
  | "straight"
  | "off_shoulder";

export type MaleShape =
  | "a_line"
  | "straight"
  | "pathani"
  | "anarkali"
  | "kaftan"
  | "pakistani_style";

export type MaleTransparency =
  | "opaque"
  | "sheer"
  | "semi_sheer";

export type MaleLength =
  | "regular"
  | "three_fourth_length"
  | "cropped";

export type MaleWaistRise =
  | "mid_rise"
  | "low_rise"
  | "high_rise";

export type MaleFade =
  | "no_fade"
  | "light_fade"
  | "heavy_fade";

export type FemaleApparelType =
  | "tops"
  | "tshirts"
  | "shirts"
  | "blouses"
  | "sweaters"
  | "sweatshirts"
  | "jackets"
  | "coats"
  | "blazers"
  | "shrug"
  | "kurtas"
  | "kurtis"
  | "dupatta"
  | "tunics"
  | "shawl"
  | "lehenga_choli"
  | "nehru_jackets"
  | "waistcoat"
  | "rain_jacket"
  | "dress"
  | "saree"
  | "skirt"
  | "jumpsuit"
  | "playsuit"
  | "coords"
  | "jeans"
  | "casual_trouser"
  | "formal_trouser"
  | "shorts"
  | "track_pants"
  | "legging"
  | "palazzo"
  // Add footwear types
  | "sports_shoes"
  | "boots"
  | "heels"
  | "casual_shoes"
  | "flats"
  | "mules"
  | "sandals"
  | "pumps"
  | "gladiators"
  | "one_toe_flats"
  | "open_toe_flats"
  | "t_strap_flats"
  | "mojaris"
  | "ballerinas"
  | "peep_toes"
  | "mary_janes"
  | "boat_shoes"
  | "brogues"
  | "clogs"
  | "derbys"
  | "driving_shoes"
  | "espadrilles"
  | "flatforms"
  | "loafers"
  | "monks"
  | "mule_sneakers"
  | "oxfords"
  | "skate_shoes"
  | "slip_on_sneakers"
  | "sneakers"
  | "trekking_shoes"
  | "biker_boots"
  | "chelsea_boots"
  | "chunky_boots"
  | "cowboy_boots"
  | "desert_boots"
  | "hiking_boots"
  | "monk_straps"
  | "rain_boots"
  | "regular_boots"
  | "slouchy_boots"
  | "winter_boots"
  | "salwar"
  | "churidar"
  | "patiala"
  | "dhotis"
  | "jeggings"
  | "harem_pants"
  | "rain_trousers"
  | "slip_on_shoes"
  | "slip_ons"
  | "sliders";

export type FemaleJacketType =
  | "denim_jacket"
  | "padded_jacket"
  | "sporty_jacket"
  | "quilted_jacket"
  | "tailored_jacket"
  | "puffer_jacket"
  | "bomber"
  | "gilet"
  | "open_front_jacket"
  | "biker_jacket"
  | "leather_jacket"
  | "parka"
  | "varsity_jacket"
  | "cardigan"
  | "cape_jacket"
  | "sherpa"
  | "regular";

export type FemaleFit =
  | "regular_fit"
  | "relaxed_fit"
  | "boxy"
  | "oversized"
  | "slim_fit"
  | "a_line"
  | "flared"
  | "bootcut"
  | "skinny"
  | "tapered"
  | "straight"
  | "mom_fit"
  | "wide"
  // Adding missing fits
  | "loose_fit"
  | "wide_leg"
  | "skinny_fit"
  | "baggy"
  | "boyfriend_fit"
  | "slouchy_fit"
  | "super_skinny_fit"
  | "tailored_fit"
  | "dad_fit"
  | "jogger"
  | "barrel_fit"
  | "compression";

export type FemaleShape =
  | "a_line"
  | "straight"
  | "anarkali"
  | "kaftan"
  | "fit_and_flare"
  | "maxi"
  | "bodycon"
  | "drop_waist"
  | "empire"
  | "fit_flare"
  | "sheath"
  | "pinafore"
  | "peplum"
  | "structured"
  | "pathani";

export type FemaleSleeveType =
  | "regular_sleeves"
  | "flared_sleeves"
  | "puff_sleeves"
  | "no_sleeves"
  | "roll_up_sleeves"
  | "shoulder_straps"
  | "bell_sleeves"
  | "cold_shoulder_sleeves"
  | "extended_sleeves"
  | "cap_sleeves"
  | "accordion_pleated"
  | "batwing"
  | "bishop"
  | "cape"
  | "cold_shoulder"
  | "cuffed"
  | "flutter"
  | "kimono"
  | "raglan"
  | "slit"
  | "ruffle";

export type FemaleSleeves =
  | "long"
  | "short"
  | "sleeveless"
  | "three_quarter"
  | "no_sleeves"
  | "regular"
  | "shoulder_straps";

export type FemaleNeckline =
  | "round_neck"
  | "mandarin_collar"
  | "v_neck"
  | "band_collar"
  | "tie_up_neck"
  | "keyhole_neck"
  | "shirt_collar"
  | "shoulder_straps"
  | "sweetheart_neck"
  | "boat_neck"
  | "square_neck"
  | "stylised_neck"
  | "scoop_neck"
  | "u_neck"
  | "halter_neck"
  | "turtle_neck"
  | "off_shoulder"
  | "shawl_collar"
  | "mock_collar"
  | "hood"
  | "cowl_neck"
  | "lapel_collar"
  | "henley_neck"
  | "peter_pan_collar"
  | "high_neck"
  | "shawl_neck"
  | "crew"
  | "jewel_neck"
  | "choker"
  | "one_shoulder"
  | "polo";

export type FemaleLength =
  | "regular"
  | "crop"
  | "cropped"
  | "longline"
  | "mini"
  | "midi"
  | "maxi"
  | "above_knee"
  | "knee_length"
  | "high_low";

export type FemalePattern =
  | "solid"
  | "geometric"
  | "geometric_printed"
  | "floral"
  | "floral_printed"
  | "washed"
  | "striped"
  | "self_design"
  | "textured_self_design"
  | "abstract"
  | "abstract_printed"
  | "ethnic_motifs"
  | "woven_design"
  | "colourblocked"
  | "bandhani"
  | "tribal"
  | "embellished"
  | "checked"
  | "paisley"
  | "brand_logo"
  | "brand_logo_printed"
  | "typography"
  | "animal"
  | "animal_printed"
  | "ombre"
  | "graphic"
  | "camouflage"
  | "camouflage_printed"
  | "conversational"
  | "polka_dots"
  | "houndstooth"
  | "chevron"
  | "leheriya"
  | "quirky"
  | "tie_and_dye"
  | "candy_stripes"
  | "cartoon_characters"
  | "dyed"
  | "painted"
  | "sequinned_stripes"
  | "tropical"
  | "vertical_stripes"
  | "diagonal"
  | "big_prints"
  | "small_prints";

export type FemaleFabric =
  | "cotton"
  | "nylon"
  | "polyester"
  | "pure_cotton"
  | "denim"
  | "viscose_rayon"
  | "liva"
  | "linen"
  | "chanderi"
  | "wool"
  | "crepe"
  | "silk"
  | "velvet"
  | "fleece"
  | "pu"
  | "leather"
  | "modal"
  | "blended"
  | "corduroy"
  | "georgette"
  | "silk_blend"
  | "satin"
  | "acrylic"
  | "suede"
  | "chiffon"
  | "elastane"
  | "organza"
  | "synthetic"
  | "knit"
  | "jacquard"
  | "linen_blend"
  | "viscose_blend"
  | "cotton_blend"
  | "chambray"
  | "crochet"
  | "dobby"
  | "net"
  | "tweed"
  | "canvas"
  | "cashmere"
  | "khadi"
  | "lyocell"
  | "organic_cotton"
  | "poly_silk"
  | "flax"
  | "tencel"
  | "jute_silk"
  | "polyester_pu_coated"
  | "voile"
  | "bamboo"
  | "cotton_wool";

export type FemaleTransparency =
  | "opaque"
  | "sheer"
  | "semi_sheer";

export type FemaleWaistRise =
  | "high_rise"
  | "mid_rise"
  | "low_rise";

export type FootwearFit =
  | "slim_fit"
  | "regular_fit"
  | "tailored_fit"
  | "loose_fit";

export type FootwearMaterial =
  | "suede"
  | "pu"
  | "mesh"
  | "synthetic"
  | "textile"
  | "leather"
  | "canvas"
  | "synthetic_leather"
  | "synthetic_patent"
  | "synthetic_suede"
  | "velvet"
  | "nubuck"
  | "patent_leather"
  | "croslite"
  | "plastic"
  | "rubber"
  | "fabric"
  | "fur"
  | "faux_fur"
  | "lace"
  | "cotton"
  | "polyester"
  | "nylon";

export type FootwearAnkleLength =
  | "regular"
  | "mid_top"
  | "high_top";

export type FootwearFastening =
  | "ankle_loop"
  | "backstrap"
  | "buckles"
  | "lace_ups"
  | "no_back_strap"
  | "slip_ons"
  | "velcro"
  | "zip";

export type FootwearToeType =
  | "open_toe"
  | "peep_toe"
  | "pointed_toe"
  | "round_toe"
  | "square_toe"
  | "stilleto";

export type FootwearHeelType =
  | "block"
  | "comfort"
  | "flatform"
  | "kitten"
  | "platform"
  | "slim"
  | "stilleto"
  | "wedge";

export type FootwearInsole =
  | "eva_or_rubber"
  | "comfort_insole"
  | "padded"
  | "memory_foam"
  | "arch_support"
  | "support_insole"
  | "custom_orthotics"
  | "leather"
  | "poron"
  | "athletic"
  | "fur";

export interface MaleApparelMetaData extends BaseApparelMetaData {
  apparelCategory: ApparelCategory;
  apparelProfile: "MALE";
  apparelType: MaleApparelType;
  pattern?: MalePattern;
  colour?: string;
  fit?: MaleFit | FootwearFit;
  length?: MaleLength;
  fabric?: MaleFabric | FootwearMaterial;
  sleeves?: MaleSleeves;
  neckLineORCollar?: MaleNeckline | MaleCollar;
  shape?: MaleShape;
  fade?: MaleFade;
  stretch?: "stretchable" | "non_stretchable";
  waistRise?: MaleWaistRise;
  transparency?: MaleTransparency;
  // Footwear-specific fields
  ankleLength?: FootwearAnkleLength;
  fastening?: FootwearFastening;
  toeType?: FootwearToeType;
  heelType?: FootwearHeelType;
  insole?: FootwearInsole;
}

export interface FemaleApparelMetaData extends BaseApparelMetaData {
  apparelCategory: ApparelCategory;
  apparelProfile: "FEMALE";
  apparelType: FemaleApparelType;
  jacketType?: FemaleJacketType;
  pattern?: FemalePattern;
  colour?: string;
  fit?: FemaleFit | FootwearFit;
  shape?: FemaleShape;
  sleeveType?: FemaleSleeveType;
  sleeves?: FemaleSleeves;
  fabric?: FemaleFabric | FootwearMaterial;
  neckLineORCollar?: FemaleNeckline;
  length?: FemaleLength;
  waistRise?: FemaleWaistRise;
  transparency?: FemaleTransparency;
  stretch?: "stretchable" | "non_stretchable";
  // Footwear-specific fields
  ankleLength?: FootwearAnkleLength;
  fastening?: FootwearFastening;
  toeType?: FootwearToeType;
  heelType?: FootwearHeelType;
  insole?: FootwearInsole;
}

export type ApparelMetaData = MaleApparelMetaData | FemaleApparelMetaData;

// Common types for both male and female
export type ApparelType = MaleApparelType | FemaleApparelType;

// Request interfaces
export interface CreateApparelRequest {
  source: ApparelSource;
  sourceId?: string;
  apparelMetaData: ApparelMetaData;
}

export interface UpdateApparelRequest {
  source: ApparelSource;
  sourceId: string;
  apparelMetaData: ApparelMetaData;
}

export interface ListApparelsRequest {
  userId: string;
}

export interface UpdateWardrobeInput {
  wardrobeApparelStatus?: ApparelStatus;
  outfitCombinations?: string[];
  apparel?: Partial<Apparel>;
}

// Response interfaces
export interface ApparelResponse {
  statusCode: number;
  body: any;
  errorCode?: string;
}

// Main apparel type
export type Apparel = ApparelMetaData & {
  source: ApparelSource;
  sourceId: string;
  createdDate: string;
  updatedDate: string;
  updatedBy?: ApparelSource;
}

export interface UserWardrobeInfo {
  userId: string;
  apparelId: string;
  wardrobeApparelStatus: ApparelStatus;
  outfitCombinations: string[];
  apparel: Apparel;
}

// Elasticsearch Document Type
export interface ApparelDocument {
  neckLineORCollar: MaleNeckline;
  shape: MaleShape;
  fade: MaleFade;
  waistRise: MaleWaistRise;
  transparency: MaleTransparency;
  ankleLength: FootwearAnkleLength;
  fastening: FootwearFastening;
  toeType: FootwearToeType;
  heelType: FootwearHeelType;
  insole: FootwearInsole;
  jacketType: FemaleJacketType;
  productId: number;
  productName: string;
  imageUrl: string;
  category: string;
  apparelProfile: string;
  apparelCategory: string;
  primaryColor: string;
  secondaryColor: string;
  productUrl: string;
  brand: string;
  clothingType: string;
  fit: string;
  fabric: string;
  length: string;
  sleeveType: string;
  sleeves: string;
  pattern: string;
  updatedAt: string;
  colour: string;
  occasion: string;
  apparelPrimaryColorHex: string;
  apparelSecondaryColorHex: string;
  primaryHumanReadableColor: string;
  secondaryHumanReadableColor: string;
  original_image_url: string;
  hero_image_url: string;
  material: FootwearMaterial;
}

// Search field types for type safety
export type SearchableField = keyof ApparelDocument;

// Define search field boosts
export interface SearchFieldBoost {
  field: SearchableField;
  boost: number;
}

// Define the search fields with their boosts
export const SEARCH_FIELD_BOOSTS: SearchFieldBoost[] = [
  // Primary search fields - highest boost
  { field: 'productName', boost: 4 },
  { field: 'brand', boost: 4 },
  { field: 'primaryHumanReadableColor', boost: 4 },

  // Secondary search fields - medium boost
  { field: 'apparelCategory', boost: 3 },
  { field: 'category', boost: 3 },
  { field: 'clothingType', boost: 3 },

  // Tertiary search fields - lower boost
  { field: 'secondaryHumanReadableColor', boost: 2 },
  { field: 'pattern', boost: 2 },
  { field: 'fabric', boost: 2 },

  // Descriptive fields - normal boost
  { field: 'fit', boost: 1 },
  { field: 'sleeves', boost: 1 },
  { field: 'neckLineORCollar', boost: 1 },
  { field: 'transparency', boost: 1 }
];

// Helper function to convert SearchFieldBoost[] to Elasticsearch search fields format
export function getSearchFields(): string[] {
  return SEARCH_FIELD_BOOSTS.map(field => `${field.field}^${field.boost}`);
}

/**
 * Request interface for the createApparelsFromImages method
 */
export interface CreateApparelsFromImagesRequest {
  userId: string;
  imageUrls: string[];
  gender: ApparelProfile;
}

/**
 * Response interface for the createApparelsFromImages method
 */
export interface CreateApparelsFromImagesResponse {
  success: boolean;
  message: string;
  /** Array of successfully created apparels */
  apparels: UserWardrobeInfo[];
  /** Array of duplicate or failed images with their descriptions */
  duplicates: { imageUrl: string; apparelDescriptions: string[] }[];
  /** Total number of images that were processed */
  processedImages: number;
  /** Number of images that were successfully processed */
  successfullyProcessed: number;
  timestamp: string;
}