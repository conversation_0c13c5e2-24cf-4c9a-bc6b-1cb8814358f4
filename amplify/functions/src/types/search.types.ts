// Base Types
export interface ResponseMetadata {
  requestId: string;
  timestamp: string;
  processingTime: number
}

export interface ApiResponseBase {
  success: boolean;
  message?: string;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata: ResponseMetadata;
}

// Text Search Types
export interface TextSearchParams {
  textQuery: string;
  pageSize?: number;
  offset?: number;
  countryCode?: string;
}

export interface TextSearchResponse {
  results?: Array<{
    document?: {
      derivedStructData?: {
        title?: string;
        link?: string;
        snippets?: Array<{
          snippet?: string;
          htmlSnippet?: string;
        }>;
        pagemap?: {
          cse_image?: Array<{ src?: string }>;
          cse_thumbnail?: Array<{ src?: string; width?: string; height?: string }>;
          metatags?: Array<{
            'og:title'?: string;
            'og:description'?: string;
            'og:image'?: string;
            'og:url'?: string;
            'twitter:title'?: string;
            'twitter:description'?: string;
            'twitter:image'?: string;
            'twitter:site'?: string;
          }>;
          hproduct?: Array<{
            fn?: string;
            photo?: string;
          }>;
        };
        formattedUrl?: string;
        displayLink?: string;
        htmlTitle?: string;
        htmlFormattedUrl?: string;
      };
    };
  }>;
  totalSize?: number;
  attributionToken?: string;
  nextPageToken?: string;
  summary?: Record<string, unknown>;
}

export interface TextSearchApiResponse extends ApiResponseBase {
  data: TextSearchResult[];
  pagination?: {
    currentPage: number;
    totalResults: number;
    totalSize?: number;
    hasMore: boolean;
  };
}

export interface CseImage {
  src: string;
}

export interface CseThumbnail {
  src: string;
  width?: string;
  height?: string;
}

export interface HProduct {
  fn?: string;
  photo?: string;
}

export interface Metatags {
  'og:title'?: string;
  'og:description'?: string;
  'og:image'?: string;
  'og:url'?: string;
  'twitter:title'?: string;
  'twitter:description'?: string;
  'twitter:image'?: string;
  'twitter:site'?: string;
}

export interface TextSearchResult {
  link: string;
  cseImage: CseImage | null;
  metatags: Metatags | null;
  cseThumbnail: CseThumbnail | null;
  hproduct: HProduct | null;
}

// Image Search Types
export interface ImageSearchParams {
  imageQuery: string;
  geoLocation?: string;
  isUserUpload?: boolean;
}

export interface ImageSearchRawResponse {
  results: Array<{
    content: {
      results: {
        organic: Array<{
          pos: number;
          url: string;
          price?: string;
          title: string;
          domain: string;
          pos_overall: number;
          url_thumbnail?: string;
        }>;
      };
    };
  }>;
}

export interface ImageSearchResult {
  position: number;
  url: string;
  price?: string;
  title: string;
  domain: string;
  overallPosition: number;
  thumbnail?: string;
}

export interface ImageSearchApiResponse extends ApiResponseBase {
  data: ImageSearchResult[];
  stats?: {
    totalResults: number;
    filteredResults: number;
    domainBreakdown: {
      [domain: string]: number;
    };
  };
}

// Validation Types
export interface ValidationResult {
  isValid: boolean;
  errors?: string[];
} 