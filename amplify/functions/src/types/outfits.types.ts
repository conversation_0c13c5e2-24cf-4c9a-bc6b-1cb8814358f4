export enum OutfitStatus {
  PRIMARY = "PRIMARY",
  WISHLIST = "WISHLIST",
  ARCHIVED = "ARCHIVED",
}

import { ApparelCategory, ApparelProfile, ApparelVibe } from "./apparel.types";

export interface OutfitStore {
  userId: string;
  outfitId: string;
  outfitName: string;
  apparelPointers: string[];
  dateCreated: string;
  status: OutfitStatus;
  situationContext: SituationalContext; // Required field: Situation context for the outfit
  tags?: string[]; // New field: List of ApparelVibe tags
  outfitCollectionId?: string; // New field: ID of the collection this outfit belongs to
}

export interface OutfitCollection {
  userId: string;
  outfitCollectionId: string;
  outfitCollectionName: string;
  outfitCollectionMediaUrl: string; // URL for the emoji
  outfitPointers: string[]; // List of outfit IDs in this collection
  dateCreated: string;
}

//TODO : We should allow multiple outfits for the same vibe for the same day.
export interface WeeklyOutfitCollection {
  userId: string;
  outfitCollectionId: string;
  outfitCollectionName: string;
  outfits: {
    [day: string]: {
      [vibe: string]: OutfitStore;
    };
  };
  dateCreated: string;
}

export interface EmojiElement {
  emojiId: string;
  emojiImageUrl: string;
  emojiDescription: string;
}

// Input interfaces for service methods
export interface CreateOutfitCollectionInput {
  userId: string;
  outfitCollectionName: string;
  outfitPointers: string[];
}

export interface UpdateOutfitCollectionInput {
  outfitCollectionName?: string;
}

export interface CurateOutfitsParams {
  userId: string;
  time?: string;
  location?: string;
  weather?: string;
}

export interface SituationalContext {
  eventOutfitVibe: string;
  eventTime: string;
  eventLocation: string;
  eventWeather: string;
}

// Generic interface for apparel items from AI response
export interface GenericApparelItem {
  apparelId: string;
  apparelType: string;
  apparelProfile: ApparelProfile;
  apparelCategory?: ApparelCategory;
  colour?: string;
  pattern?: string;
  fit?: string;
  fabric?: string;
  length?: string;
  sleeves?: string;
  shape?: string;
  waistRise?: string;
  neckLineORCollar?: string;
  vibe?: ApparelVibe;
  brand?: string;
  source?: string;
  // Allow for additional dynamic properties
  [key: string]: any;
}

// Interface for the AI's outfit curation response
export interface OutfitCurationResponse {
  outfitName: string;
  apparelItems: GenericApparelItem[];
}

export interface CreateOutfitInput {
  userId: string;
  outfitName: string;
  apparelPointers: string[];
  status: OutfitStatus;
  situationContext: SituationalContext; // Required field: Situation context for the outfit
  tags?: string[]; // ApparelVibe tags
  outfitCollectionId?: string; // ID of collection this outfit belongs to
}

export interface UpdateOutfitInput {
  status?: OutfitStatus;
  outfitCollectionId?: string | null; // ID of collection this outfit belongs to
}

export class OutfitError extends Error {
  constructor(
    message: string,
    public readonly statusCode: number = 500,
    public readonly errorCode?: string
  ) {
    super(message);
    this.name = "OutfitError";
  }
}