import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import {
  DynamoDBDocumentClient,
  PutCommand,
  GetCommand,
  QueryCommand,
  UpdateCommand,
  DeleteCommand,
} from "@aws-sdk/lib-dynamodb";
import {
  ApparelProfile,
  UpdateWardrobeInput,
  UserWardrobeInfo,
  Apparel,
  ApparelSource,
  ApparelMetaData,
  ApparelStatus,
  ApparelCategory
} from "../types/apparel.types";

// Interface for filtering wardrobe items
export interface WardrobeItemFilters {
  apparelCategory?: ApparelCategory;
  apparelStatus?: ApparelStatus;
  source?: ApparelSource;
}

const client = new DynamoDBClient({ region: "us-east-1" });
const docClient = DynamoDBDocumentClient.from(client, {
  marshallOptions: {
    removeUndefinedValues: true
  }
});

const WARDROBE_TABLE = "user_wardrobe_monova_dev";

export class UserWardrobeDao {
  private static instance: UserWardrobeDao;

  // Implement singleton pattern
  public static getInstance(): UserWardrobeDao {
    if (!UserWardrobeDao.instance) {
      UserWardrobeDao.instance = new UserWardrobeDao();
    }
    return UserWardrobeDao.instance;
  }

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  async addToWardrobe(
    userId: string,
    apparelId: string,
    apparelMetaData: ApparelMetaData,
    source: ApparelSource,
    sourceId: string
  ): Promise<UserWardrobeInfo> {
    console.log("[UserWardrobeDao][addToWardrobe] Adding:", { userId, apparelId, source });

    try {
      const timestamp = new Date().toISOString();
      const apparel: Apparel = {
        ...apparelMetaData,
        source,
        sourceId,
        createdDate: timestamp,
        updatedDate: timestamp,
      };

      const item: UserWardrobeInfo = {
        userId,
        apparelId,
        wardrobeApparelStatus: ApparelStatus.PRIMARY,
        outfitCombinations: [],
        apparel,
      };

      try {
        // Use a condition expression to ensure the item doesn't already exist
        await docClient.send(
          new PutCommand({
            TableName: WARDROBE_TABLE,
            Item: item,
            ConditionExpression: "attribute_not_exists(userId) AND attribute_not_exists(apparelId)",
          })
        );
      } catch (dbError: any) {
        // Check if this is a conditional check failure (item already exists)
        if (dbError.name === "ConditionalCheckFailedException") {
          console.log("[UserWardrobeDao][addToWardrobe] Apparel already exists:", { userId, apparelId });
          throw new Error(`Apparel with ID ${apparelId} already exists for user ${userId}. Use updateWardrobeItem to modify existing apparels.`);
        }
        // Re-throw other errors
        throw dbError;
      }

      console.log("[UserWardrobeDao][addToWardrobe] Created:", item);
      return item;
    } catch (error) {
      console.error("[UserWardrobeDao][addToWardrobe] Error:", error);
      throw error;
    }
  }

  async getWardrobeItem(
    userId: string,
    apparelId: string
  ): Promise<UserWardrobeInfo | null> {
    console.log("[UserWardrobeDao][getWardrobeItem] Getting:", { userId, apparelId });

    try {
      const { Item } = await docClient.send(
        new GetCommand({
          TableName: WARDROBE_TABLE,
          Key: { userId, apparelId },
        })
      );

      return (Item as UserWardrobeInfo) || null;
    } catch (error) {
      console.error("[UserWardrobeDao][getWardrobeItem] Error:", error);
      throw error;
    }
  }

  async listWardrobeItems(
    userId: string,
    limit = 50,
    startKey?: any,
    profile?: ApparelProfile
  ): Promise<{ items: UserWardrobeInfo[]; lastKey?: any }> {
    console.log("[UserWardrobeDao][listWardrobeItems] Listing:", { userId, profile: profile || "all" });

    try {
      let params: any = {
        TableName: WARDROBE_TABLE,
        KeyConditionExpression: "userId = :userId",
        ExpressionAttributeValues: { ":userId": userId },
        Limit: limit,
      };

      // If profile is specified, filter by profile
      if (profile) {
        params.FilterExpression = "apparel.apparelProfile = :profile";
        params.ExpressionAttributeValues[":profile"] = profile;
      }

      if (startKey) params.ExclusiveStartKey = startKey;

      const { Items, LastEvaluatedKey } = await docClient.send(
        new QueryCommand(params)
      );

      return {
        items: Items as UserWardrobeInfo[],
        lastKey: LastEvaluatedKey,
      };
    } catch (error) {
      console.error("[UserWardrobeDao][listWardrobeItems] Error:", error);
      throw error;
    }
  }

  async updateWardrobeItem(
    userId: string,
    apparelId: string,
    updates: UpdateWardrobeInput
  ): Promise<UserWardrobeInfo> {
    console.log("[UserWardrobeDao][updateWardrobeItem] Updating:", { userId, apparelId, updates });

    try {
      let updateExpressions: string[] = [];
      const expressionAttributeValues: any = {};
      const expressionAttributeNames: any = {};

      // Handle wardrobe-specific updates
      if (updates.wardrobeApparelStatus !== undefined) {
        updateExpressions.push("#status = :status");
        expressionAttributeNames["#status"] = "wardrobeApparelStatus";
        expressionAttributeValues[":status"] = updates.wardrobeApparelStatus;
      }

      if (updates.outfitCombinations !== undefined) {
        updateExpressions.push("#combinations = :combinations");
        expressionAttributeNames["#combinations"] = "outfitCombinations";
        expressionAttributeValues[":combinations"] = updates.outfitCombinations;
      }

      // Handle apparel updates if present
      if (updates.apparel) {
        // Update the entire apparel object
        updateExpressions.push("#apparel = :apparel");
        expressionAttributeNames["#apparel"] = "apparel";
        expressionAttributeValues[":apparel"] = {
          ...updates.apparel,
          updatedDate: new Date().toISOString()
        };
      }

      const { Attributes } = await docClient.send(
        new UpdateCommand({
          TableName: WARDROBE_TABLE,
          Key: { userId, apparelId },
          UpdateExpression: `set ${updateExpressions.join(", ")}`,
          ExpressionAttributeNames: expressionAttributeNames,
          ExpressionAttributeValues: expressionAttributeValues,
          ReturnValues: "ALL_NEW",
        })
      );

      return Attributes as UserWardrobeInfo;
    } catch (error) {
      console.error("[UserWardrobeDao][updateWardrobeItem] Error:", error);
      throw error;
    }
  }

  async removeFromWardrobe(userId: string, apparelId: string): Promise<void> {
    console.log("[UserWardrobeDao][removeFromWardrobe] Deleting:", { userId, apparelId });

    try {
      await docClient.send(
        new DeleteCommand({
          TableName: WARDROBE_TABLE,
          Key: { userId, apparelId },
        })
      );
    } catch (error) {
      console.error("[UserWardrobeDao][removeFromWardrobe] Error:", error);
      throw error;
    }
  }

  /**
   * List wardrobe items with filters
   * @param userId The user ID
   * @param filters Optional filters to apply to the query
   * @param limit Maximum number of items to return
   * @param startKey Optional starting key for pagination
   * @returns Filtered wardrobe items and last evaluated key for pagination
   */
  async listWardrobeItemsWithFilters(
    userId: string,
    filters?: WardrobeItemFilters,
    limit = 50,
    startKey?: any
  ): Promise<{ items: UserWardrobeInfo[]; lastKey?: any }> {
    console.log("[UserWardrobeDao][listWardrobeItemsWithFilters] Listing with filters:", {
      userId,
      filters: filters ? JSON.stringify(filters) : "none"
    });

    try {
      // Base query parameters
      let params: any = {
        TableName: WARDROBE_TABLE,
        KeyConditionExpression: "userId = :userId",
        ExpressionAttributeValues: { ":userId": userId },
        Limit: limit,
      };

      // Add start key for pagination if provided
      if (startKey) params.ExclusiveStartKey = startKey;

      // If no filters, just return all items
      if (!filters || Object.keys(filters).length === 0) {
        const { Items, LastEvaluatedKey } = await docClient.send(
          new QueryCommand(params)
        );

        return {
          items: Items as UserWardrobeInfo[],
          lastKey: LastEvaluatedKey,
        };
      }

      // Build filter expression and attribute values
      const filterExpressions: string[] = [];
      const expressionAttributeNames: Record<string, string> = {};

      // Handle apparelCategory filter
      if (filters.apparelCategory) {
        filterExpressions.push("#apparel.#apparelCategory = :apparelCategory");
        expressionAttributeNames["#apparel"] = "apparel";
        expressionAttributeNames["#apparelCategory"] = "apparelCategory";
        params.ExpressionAttributeValues[":apparelCategory"] = filters.apparelCategory;
      }

      // Handle apparelStatus filter
      if (filters.apparelStatus) {
        filterExpressions.push("#wardrobeApparelStatus = :apparelStatus");
        expressionAttributeNames["#wardrobeApparelStatus"] = "wardrobeApparelStatus";
        params.ExpressionAttributeValues[":apparelStatus"] = filters.apparelStatus;
      }

      // Handle source filter
      if (filters.source) {
        filterExpressions.push("#apparel.#source = :source");
        expressionAttributeNames["#apparel"] = "apparel";
        expressionAttributeNames["#source"] = "source";
        params.ExpressionAttributeValues[":source"] = filters.source;
      }

      // Add filter expression if we have any filters
      if (filterExpressions.length > 0) {
        params.FilterExpression = filterExpressions.join(" AND ");
        params.ExpressionAttributeNames = expressionAttributeNames;
      }

      console.log("[UserWardrobeDao][listWardrobeItemsWithFilters] Query params:", JSON.stringify(params, null, 2));

      // Execute the query
      const { Items, LastEvaluatedKey } = await docClient.send(
        new QueryCommand(params)
      );

      console.log(`[UserWardrobeDao][listWardrobeItemsWithFilters] Found ${Items?.length || 0} items`);

      return {
        items: Items as UserWardrobeInfo[],
        lastKey: LastEvaluatedKey,
      };
    } catch (error) {
      console.error("[UserWardrobeDao][listWardrobeItemsWithFilters] Error:", error);
      throw error;
    }
  }
}