import { Client } from "@elastic/elasticsearch";
import { ApparelDocument, getSearchFields } from "../types/apparel.types";

// Define search fields to include both original fields and new ones from enriched data
const searchFields = getSearchFields();

// Add this interface near the top of the file
interface TermsAggregation {
  buckets: Array<{
    key: string;
    doc_count: number;
  }>;
}

export class ApparelDao {
  private client: Client;
  private indexName = "central_apparel_data_store_dev";

  constructor(
    apiKey: string = "NXk0RG1aVUJqLTRCRDBJQjczSTc6b2dTWG41OENUQnE0aXNVLU5rZHhoUQ=="
  ) {
    console.log("[ApparelDao] Initializing with Elasticsearch...");
    this.client = new Client({
      node: "https://9cd1c33a47ce45eebcb935c313b8c0f4.us-east4.gcp.elastic-cloud.com:443",
      auth: {
        apiKey: api<PERSON><PERSON>,
      },
    });
  }

  async getSuggestions(
    query: string,
    apparelProfile?: string,
    limit: number = 5
  ): Promise<string[]> {
    if (!query || query.length < 2) {
      return [];
    }

    console.log(
      `[ApparelDao] Getting suggestions for: ${query}, profile: ${apparelProfile}`
    );

    try {
      // Build the search body using the suggest API
      const searchBody: any = {
        size: 0, // We don't need actual documents, just suggestions
        suggest: {
          product_name_suggestions: {
            prefix: query,
            completion: {
              field: "productName.completion",
              size: limit,
              skip_duplicates: true,
              fuzzy: {
                fuzziness: "AUTO",
              },
            },
          },
          brand_suggestions: {
            prefix: query,
            completion: {
              field: "brand.completion",
              size: limit,
              skip_duplicates: true,
              fuzzy: {
                fuzziness: "AUTO",
              },
            },
          },
          category_suggestions: {
            prefix: query,
            completion: {
              field: "apparelCategory.completion",
              size: limit,
              skip_duplicates: true,
              fuzzy: {
                fuzziness: "AUTO",
              },
            },
          },
        },
      };

      // If apparelProfile is provided, add a filter to the suggestions
      if (apparelProfile) {
        // Apply filter context to each suggester
        Object.keys(searchBody.suggest).forEach((suggesterKey) => {
          searchBody.suggest[suggesterKey].completion.contexts = {
            apparelProfile: [apparelProfile.toLowerCase()],
          };
        });
      }

      // If the completion suggester isn't set up, use a backup approach with a prefix query
      const backupBody: any = {
        size: 0,
        query: {
          bool: {
            should: [
              { prefix: { productName: { value: query, boost: 4 } } },
              { prefix: { brand: { value: query, boost: 4 } } },
              { prefix: { apparelCategory: { value: query, boost: 3 } } },
              { prefix: { category: { value: query, boost: 3 } } },
              { prefix: { clothingType: { value: query, boost: 3 } } },
            ],
            minimum_should_match: 1,
          },
        },
        aggs: {
          product_name_suggestions: {
            terms: {
              field: "productName.keyword",
              size: limit * 2,
            },
          },
          brand_suggestions: {
            terms: {
              field: "brand.keyword",
              size: limit * 2,
            },
          },
          category_suggestions: {
            terms: {
              field: "apparelCategory.keyword",
              size: limit,
            },
          },
          clothing_type_suggestions: {
            terms: {
              field: "clothingType.keyword",
              size: limit,
            },
          },
        },
      };

      // Add apparelProfile filter if provided to the backup query
      if (apparelProfile) {
        backupBody.query.bool.filter = [
          { term: { apparelProfile: apparelProfile.toLowerCase() } },
        ];
      }

      // Try the suggest API first
      let response;
      let suggestions: string[] = [];
      const addedSuggestions = new Set<string>();

      try {
        response = await this.client.search({
          index: this.indexName,
          body: searchBody,
        });

        // Check if we got any suggestions
        if (response.suggest) {
          // Process each suggester's results
          for (const suggesterKey of Object.keys(response.suggest)) {
            const suggester = response.suggest[suggesterKey];
            if (suggester && suggester.length > 0 && Array.isArray(suggester[0].options)) {
              for (const option of suggester[0].options) {
                const suggestion = option.text;
                if (
                  !addedSuggestions.has(suggestion.toLowerCase()) &&
                  suggestions.length < limit
                ) {
                  suggestions.push(suggestion);
                  addedSuggestions.add(suggestion.toLowerCase());
                }
              }
            }
          }
        }
      } catch (error) {
        console.log(
          "[ApparelDao] Suggest API failed, falling back to prefix query:",
          error
        );
      }

      // If we got no suggestions, fall back to the prefix query
      if (suggestions.length === 0) {
        response = await this.client.search({
          index: this.indexName,
          body: backupBody,
        });

        // Process aggregations to get suggestions
        const aggregations = response.aggregations;
        if (aggregations) {
          // Helper function to add suggestions from aggregation buckets
          const addSuggestionsFromAgg = (agg: TermsAggregation) => {
            if (agg && agg.buckets) {
              for (const bucket of agg.buckets) {
                const suggestionText = bucket.key;
                if (
                  suggestionText &&
                  !addedSuggestions.has(suggestionText.toLowerCase()) &&
                  suggestions.length < limit
                ) {
                  suggestions.push(suggestionText);
                  addedSuggestions.add(suggestionText.toLowerCase());
                }
              }
            }
          };

          // Process each aggregation in priority order
          if (aggregations.product_name_suggestions) {
            addSuggestionsFromAgg(
              aggregations.product_name_suggestions as TermsAggregation
            );
          }
          if (aggregations.brand_suggestions) {
            addSuggestionsFromAgg(
              aggregations.brand_suggestions as TermsAggregation
            );
          }
          if (aggregations.category_suggestions) {
            addSuggestionsFromAgg(
              aggregations.category_suggestions as TermsAggregation
            );
          }
          if (aggregations.clothing_type_suggestions) {
            addSuggestionsFromAgg(
              aggregations.clothing_type_suggestions as TermsAggregation
            );
          }
        }
      }

      console.log(`[ApparelDao] Returning ${suggestions.length} suggestions`);
      return suggestions;
    } catch (error) {
      console.error("[ApparelDao] Error getting suggestions:", error);
      // Return empty array instead of throwing, to provide a more resilient API
      return [];
    }
  }

  async queryInventoryForApparels(
    searchBody: any,
    page: number = 1,
    limit: number = 20
  ): Promise<{ results: ApparelDocument[]; total: number }> {
    console.log("[ApparelDao] Searching apparels with body:", {
      searchBody,
      page,
      limit,
    });
    try {
      // Set pagination parameters
      searchBody.from = (page - 1) * limit;
      searchBody.size = limit;

      console.log("[ApparelDao] Final search body:", JSON.stringify(searchBody, null, 2));

      const response = await this.client.search({
        index: this.indexName,
        body: searchBody,
      });

      // Extract results and total count from response
      const results = response.hits.hits.map((hit: any) => {
        // Combine _source with _id and _score
        return {
          ...hit._source,
          _id: hit._id,
          _score: hit._score,
        } as ApparelDocument;
      });

      // Handle different types of total hits response
      const total =
        typeof response.hits.total === "object"
          ? response.hits.total.value
          : (response.hits.total as number) || 0;

      console.log(
        `[ApparelDao] Found ${
          results.length
        } matching apparels (page ${page} of ${Math.ceil(total / limit)})`
      );

      return { results, total };
    } catch (error) {
      console.error("[ApparelDao] Error searching apparels:", error);
      throw error;
    }
  }
}
