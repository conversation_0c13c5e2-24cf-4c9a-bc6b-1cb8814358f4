import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient } from "@aws-sdk/lib-dynamodb";

const client = new DynamoDBClient({ region: "us-east-1" });
export const docClient = DynamoDBDocumentClient.from(client);

// Table names
export const TABLES = {
  CENTRAL_APPAREL: "apparel_store_monova_dev",
  USER_WARDROBE: "user_wardrobe_monova_dev",
  OUTFIT: "outfit_monova_dev",
  OUTFIT_COLLECTION: "outfit_collection_monova_dev",
  STYLE_PROFILE: "style_profile_monova_dev",
  FEEDBACK: "feedback_monova_dev",
  OTP_HOLDER: "token", // Updated to the new table name
} as const;