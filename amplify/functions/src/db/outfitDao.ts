import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import {
  DynamoDBDocumentClient,
  PutCommand,
  GetCommand,
  QueryCommand,
  UpdateCommand,
  DeleteCommand,
} from "@aws-sdk/lib-dynamodb";
import {
  OutfitStore,
  CreateOutfitInput,
  UpdateOutfitInput,
} from "../types/outfits.types";

const client = new DynamoDBClient({ region: "us-east-1" });
const docClient = DynamoDBDocumentClient.from(client);

const OUTFIT_TABLE = "outfit_store_monova_dev";

export class OutfitDao {
  async createOutfit(input: CreateOutfitInput): Promise<OutfitStore> {
    console.log("[OutfitDao][createOutfit] Creating outfit:", input);

    try {
      const outfitId = `${Number.MAX_SAFE_INTEGER - Date.now()}_${input.userId}`;
      const item: OutfitStore = {
        userId: input.userId,
        outfitId,
        outfitName: input.outfitName,
        apparelPointers: input.apparelPointers,
        dateCreated: new Date().toISOString(),
        status: input.status,
        situationContext: input.situationContext,
        tags: input.tags,
        outfitCollectionId: input.outfitCollectionId,
      };

      await docClient.send(
        new PutCommand({
          TableName: OUTFIT_TABLE,
          Item: item,
        })
      );

      console.log("[OutfitDao][createOutfit] Created outfit:", item);
      return item;
    } catch (error) {
      console.error("[OutfitDao][createOutfit] Error:", error);
      throw error;
    }
  }

  async getOutfit(
    userId: string,
    outfitId: string
  ): Promise<OutfitStore | null> {
    console.log("[OutfitDao][getOutfit] Getting outfit:", { userId, outfitId });

    try {
      const { Item } = await docClient.send(
        new GetCommand({
          TableName: OUTFIT_TABLE,
          Key: { userId, outfitId },
        })
      );

      return (Item as OutfitStore) || null;
    } catch (error) {
      console.error("[OutfitDao][getOutfit] Error:", error);
      throw error;
    }
  }

  async listOutfits(
    userId: string,
    limit = 50,
    startKey?: any
  ): Promise<{ items: OutfitStore[]; lastKey?: any }> {
    console.log("[OutfitDao][listOutfits] Listing outfits:", { userId });

    try {
      const params = {
        TableName: OUTFIT_TABLE,
        KeyConditionExpression: "userId = :userId",
        ExpressionAttributeValues: { ":userId": userId },
        Limit: limit,
        ...(startKey && { ExclusiveStartKey: startKey }),
      };

      const { Items, LastEvaluatedKey } = await docClient.send(
        new QueryCommand(params)
      );

      return {
        items: Items as OutfitStore[],
        lastKey: LastEvaluatedKey,
      };
    } catch (error) {
      console.error("[OutfitDao][listOutfits] Error:", error);
      throw error;
    }
  }

  async updateOutfit(
    userId: string,
    outfitId: string,
    updates: UpdateOutfitInput
  ): Promise<OutfitStore> {
    console.log("[OutfitDao][updateOutfit] Updating outfit:", {
      userId,
      outfitId,
      updates,
    });

    try {
      const updateExpressions: string[] = [];
      const expressionAttributeValues: any = {};
      const expressionAttributeNames: any = {};

      Object.entries(updates).forEach(([key, value], index) => {
        if (value !== undefined) {
          updateExpressions.push(`#${key} = :${key}`);
          expressionAttributeNames[`#${key}`] = key;
          expressionAttributeValues[`:${key}`] = value;
        }
      });

      const { Attributes } = await docClient.send(
        new UpdateCommand({
          TableName: OUTFIT_TABLE,
          Key: { userId, outfitId },
          UpdateExpression: `set ${updateExpressions.join(", ")}`,
          ExpressionAttributeNames: expressionAttributeNames,
          ExpressionAttributeValues: expressionAttributeValues,
          ReturnValues: "ALL_NEW",
        })
      );

      return Attributes as OutfitStore;
    } catch (error) {
      console.error("[OutfitDao][updateOutfit] Error:", error);
      throw error;
    }
  }

  async deleteOutfit(userId: string, outfitId: string): Promise<void> {
    console.log("[OutfitDao][deleteOutfit] Deleting outfit:", {
      userId,
      outfitId,
    });

    try {
      await docClient.send(
        new DeleteCommand({
          TableName: OUTFIT_TABLE,
          Key: { userId, outfitId },
        })
      );
    } catch (error) {
      console.error("[OutfitDao][deleteOutfit] Error:", error);
      throw error;
    }
  }

  async listOutfitsByCollection(
    userId: string,
    collectionId: string,
    limit = 50,
    startKey?: any
  ): Promise<{ items: OutfitStore[]; lastKey?: any }> {
    console.log(
      "[OutfitDao][listOutfitsByCollection] Listing outfits by collection:",
      {
        userId,
        collectionId,
      }
    );

    try {
      const params = {
        TableName: OUTFIT_TABLE,
        IndexName: "byCollectionId", // Assuming a GSI is created for outfitCollectionId
        KeyConditionExpression:
          "userId = :userId AND outfitCollectionId = :collectionId",
        ExpressionAttributeValues: {
          ":userId": userId,
          ":collectionId": collectionId,
        },
        Limit: limit,
        ...(startKey && { ExclusiveStartKey: startKey }),
      };

      const { Items, LastEvaluatedKey } = await docClient.send(
        new QueryCommand(params)
      );

      return {
        items: Items as OutfitStore[],
        lastKey: LastEvaluatedKey,
      };
    } catch (error) {
      console.error("[OutfitDao][listOutfitsByCollection] Error:", error);
      throw error;
    }
  }
}
