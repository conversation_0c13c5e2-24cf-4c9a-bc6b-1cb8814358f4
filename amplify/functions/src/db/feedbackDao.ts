// feedback.dao.ts
import { Dynamo<PERSON>BClient } from "@aws-sdk/client-dynamodb";
import {
  DynamoDBDocumentClient,
  PutCommand,
  GetCommand,
  QueryCommand,
} from "@aws-sdk/lib-dynamodb";
import { Feedback } from "../types/feedback.types";

const client = new DynamoDBClient({ region: "us-east-1" });
const docClient = DynamoDBDocumentClient.from(client);

const FEEDBACK_TABLE = "user_feedback_monova_dev";

export class FeedbackDao {
  async createFeedback(feedback: Feedback): Promise<Feedback> {
    console.log("[FeedbackDao][createFeedback] Creating:", feedback);

    try {
      const timestamp = new Date().toISOString();
      const item: Feedback = {
        ...feedback,
        createdDate: timestamp,
      };

      await docClient.send(
        new PutCommand({
          TableName: FEEDBACK_TABLE,
          Item: item,
        })
      );

      console.log("[FeedbackDao][createFeedback] Created:", item);
      return item;
    } catch (error) {
      console.error("[FeedbackDao][createFeedback] Error:", error);
      throw error;
    }
  }

  async getFeedbackById(userId: string, feedbackId: string): Promise<Feedback | null> {
    console.log("[FeedbackDao][getFeedbackById] Getting:", { userId, feedbackId });

    try {
      const { Item } = await docClient.send(
        new GetCommand({
          TableName: FEEDBACK_TABLE,
          Key: { 
            userId: userId,
            feedbackId: feedbackId 
          },
        })
      );

      return (Item as Feedback) || null;
    } catch (error) {
      console.error("[FeedbackDao][getFeedbackById] Error:", error);
      throw error;
    }
  }

  async getFeedbackByUserId(userId: string): Promise<Feedback[]> {
    console.log("[FeedbackDao][getFeedbackByUserId] Getting for user:", userId);

    try {
      const { Items } = await docClient.send(
        new QueryCommand({
          TableName: FEEDBACK_TABLE,
          KeyConditionExpression: "userId = :userId",
          ExpressionAttributeValues: {
            ":userId": userId,
          },
        })
      );

      return (Items as Feedback[]) || [];
    } catch (error) {
      console.error("[FeedbackDao][getFeedbackByUserId] Error:", error);
      throw error;
    }
  }
}