import {
  DynamoDBClient,
  ConditionalCheckFailedException,
} from "@aws-sdk/client-dynamodb";
import {
  DynamoDBDocumentClient,
  PutCommand,
  GetCommand,
  UpdateCommand,
  DeleteCommand,
} from "@aws-sdk/lib-dynamodb";
import { StyleProfileStore } from "../types/style.types";
import { CreateStyleProfileInput, UpdateStyleProfileInput } from "../types/style.types";


const client = new DynamoDBClient({ region: "us-east-1" });
const docClient = DynamoDBDocumentClient.from(client);

const PROFILE_TABLE = "user_style_profile_dev";

export class StyleProfileDao {
  async createProfile(
    input: CreateStyleProfileInput
  ): Promise<StyleProfileStore> {
    console.log("[StyleProfileDao][createProfile] Creating profile:", {
      userId: input.userId,
      userGender: input.userGender,
      userBodyType: input.userBodyType,
    });

    try {
      const item: StyleProfileStore = {
        ...input,
      };

      // Check if profile exists before creating
      const existing = await this.getProfile(item.userId);
      if (existing) {
        console.warn(
          "[StyleProfileDao][createProfile] Profile already exists for user:",
          {
            userId: input.userId,
            existingProfile: {
              gender: existing.userGender,
              bodyType: existing.userBodyType,
            },
          }
        );
        throw new Error(`Profile already exists for user: ${input.userId}`);
      }

      await docClient.send(
        new PutCommand({
          TableName: PROFILE_TABLE,
          Item: item,
          // Add condition to ensure it doesn't already exist
          ConditionExpression: "attribute_not_exists(userId)",
        })
      );

      console.log(
        "[StyleProfileDao][createProfile] Profile created successfully:",
        {
          userId: input.userId,
        }
      );

      return item;
    } catch (error) {
      if (error instanceof ConditionalCheckFailedException) {
        console.error(
          "[StyleProfileDao][createProfile] Profile already exists (condition check failed):",
          {
            userId: input.userId,
            error,
          }
        );
        throw new Error(`Profile already exists for user: ${input.userId}`);
      }

      console.error(
        "[StyleProfileDao][createProfile] Error creating profile:",
        {
          userId: input.userId,
          error,
          errorType:
            error instanceof Error ? error.constructor.name : typeof error,
          errorMessage: error instanceof Error ? error.message : String(error),
        }
      );
      throw error;
    }
  }

  async getProfile(userId: string): Promise<StyleProfileStore | null> {
    console.log("[StyleProfileDao][getProfile] Getting profile:", { userId });

    try {
      const { Item } = await docClient.send(
        new GetCommand({
          TableName: PROFILE_TABLE,
          Key: { userId },
        })
      );

      if (!Item) {
        console.log("[StyleProfileDao][getProfile] Profile not found:", {
          userId,
        });
        return null;
      }

      console.log(
        "[StyleProfileDao][getProfile] Profile retrieved successfully:",
        {
          userId,
          hasProfile: !!Item,
        }
      );

      return Item as StyleProfileStore;
    } catch (error) {
      console.error("[StyleProfileDao][getProfile] Error getting profile:", {
        userId,
        error,
        errorType:
          error instanceof Error ? error.constructor.name : typeof error,
        errorMessage: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  async updateProfile(
    userId: string,
    updates: UpdateStyleProfileInput
  ): Promise<StyleProfileStore> {
    console.log("[StyleProfileDao][updateProfile] Updating profile:", {
      userId,
      updates: Object.keys(updates),
    });

    try {
      // First check if the profile exists
      const existingProfile = await this.getProfile(userId);
      if (!existingProfile) {
        console.error(
          "[StyleProfileDao][updateProfile] Cannot update non-existent profile:",
          { userId }
        );
        throw new Error(`Profile does not exist for user: ${userId}`);
      }

      const updateExpressions: string[] = [];
      const expressionAttributeValues: any = {};
      const expressionAttributeNames: any = {};

      // Only include defined values
      Object.entries(updates).forEach(([key, value]) => {
        if (value !== undefined) {
          updateExpressions.push(`#${key} = :${key}`);
          expressionAttributeNames[`#${key}`] = key;
          expressionAttributeValues[`:${key}`] = value;
        }
      });

      if (updateExpressions.length === 0) {
        console.warn(
          "[StyleProfileDao][updateProfile] No valid updates provided:",
          { userId }
        );
        return existingProfile;
      }

      const { Attributes } = await docClient.send(
        new UpdateCommand({
          TableName: PROFILE_TABLE,
          Key: { userId },
          UpdateExpression: `set ${updateExpressions.join(", ")}`,
          ExpressionAttributeNames: expressionAttributeNames,
          ExpressionAttributeValues: expressionAttributeValues,
          ConditionExpression: "attribute_exists(userId)",
          ReturnValues: "ALL_NEW",
        })
      );

      console.log(
        "[StyleProfileDao][updateProfile] Profile updated successfully:",
        {
          userId,
          updatedFields: Object.keys(updates),
        }
      );

      return Attributes as StyleProfileStore;
    } catch (error) {
      if (error instanceof ConditionalCheckFailedException) {
        console.error(
          "[StyleProfileDao][updateProfile] Profile does not exist (condition check failed):",
          {
            userId,
            error,
          }
        );
        throw new Error(`Profile does not exist for user: ${userId}`);
      }

      console.error(
        "[StyleProfileDao][updateProfile] Error updating profile:",
        {
          userId,
          error,
          errorType:
            error instanceof Error ? error.constructor.name : typeof error,
          errorMessage: error instanceof Error ? error.message : String(error),
        }
      );
      throw error;
    }
  }

  async deleteProfile(userId: string): Promise<void> {
    console.log("[StyleProfileDao][deleteProfile] Deleting profile:", {
      userId,
    });

    try {
      // First check if the profile exists
      const existingProfile = await this.getProfile(userId);
      if (!existingProfile) {
        console.warn(
          "[StyleProfileDao][deleteProfile] Cannot delete non-existent profile:",
          { userId }
        );
        throw new Error(`Profile does not exist for user: ${userId}`);
      }

      await docClient.send(
        new DeleteCommand({
          TableName: PROFILE_TABLE,
          Key: { userId },
          ConditionExpression: "attribute_exists(userId)",
        })
      );

      console.log(
        "[StyleProfileDao][deleteProfile] Profile deleted successfully:",
        { userId }
      );
    } catch (error) {
      if (error instanceof ConditionalCheckFailedException) {
        console.error(
          "[StyleProfileDao][deleteProfile] Profile does not exist (condition check failed):",
          {
            userId,
            error,
          }
        );
        throw new Error(`Profile does not exist for user: ${userId}`);
      }

      console.error(
        "[StyleProfileDao][deleteProfile] Error deleting profile:",
        {
          userId,
          error,
          errorType:
            error instanceof Error ? error.constructor.name : typeof error,
          errorMessage: error instanceof Error ? error.message : String(error),
        }
      );
      throw error;
    }
  }
}
