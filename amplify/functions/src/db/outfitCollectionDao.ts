/**
 * This is the DAO over a table which stores the style journal
 */

import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import {
  DynamoDBDocumentClient,
  PutCommand,
  GetCommand,
  QueryCommand,
  UpdateCommand,
  DeleteCommand,
} from "@aws-sdk/lib-dynamodb";
import { OutfitCollection, WeeklyOutfitCollection, OutfitStore } from "../types/outfits.types";

const client = new DynamoDBClient({ region: "us-east-1" });
const docClient = DynamoDBDocumentClient.from(client, {
  marshallOptions: {
    removeUndefinedValues: true
  }
});

const COLLECTION_TABLE = "outfit_collection_monova_dev";

export interface UpdateOutfitCollectionDaoInput {
  outfitCollectionName?: string;
  outfitCollectionMediaUrl?: string;
  outfitPointers?: string[];
}

export interface UpdateWeeklyOutfitCollectionDaoInput {
  outfits?: {
    [day: string]: {
      [vibe: string]: OutfitStore;
    };
  };
}

export class OutfitCollectionDao {
  async createOutfitCollection(
    outfitCollection: OutfitCollection
  ): Promise<OutfitCollection> {
    console.log("[OutfitCollectionDao][createOutfitCollection] Creating:", outfitCollection);

    try {
      await docClient.send(
        new PutCommand({
          TableName: COLLECTION_TABLE,
          Item: outfitCollection,
        })
      );

      console.log("[OutfitCollectionDao][createOutfitCollection] Created successfully:", outfitCollection);
      return outfitCollection;
    } catch (error) {
      console.error("[OutfitCollectionDao][createOutfitCollection] Error:", error);
      throw error;
    }
  }

  async getOutfitCollection(
    userId: string,
    outfitCollectionId: string
  ): Promise<OutfitCollection | null> {
    console.log("[OutfitCollectionDao][getOutfitCollection] Getting:", { userId, outfitCollectionId });

    try {
      const { Item } = await docClient.send(
        new GetCommand({
          TableName: COLLECTION_TABLE,
          Key: { userId, outfitCollectionId },
        })
      );

      return (Item as OutfitCollection) || null;
    } catch (error) {
      console.error("[OutfitCollectionDao][getOutfitCollection] Error:", error);
      throw error;
    }
  }

  async listOutfitCollections(
    userId: string,
    limit = 50,
    startKey?: any
  ): Promise<{ items: OutfitCollection[]; lastKey?: any }> {
    console.log("[OutfitCollectionDao][listOutfitCollections] Listing for user:", userId);
  
    try {
      const params = {
        TableName: COLLECTION_TABLE,
        KeyConditionExpression: "userId = :userId",
        ExpressionAttributeValues: { ":userId": userId },
        Limit: limit,
        ...(startKey && { ExclusiveStartKey: startKey }),
      };
  
      const { Items, LastEvaluatedKey } = await docClient.send(
        new QueryCommand(params)
      );
  
      return {
        items: Items as OutfitCollection[],
        lastKey: LastEvaluatedKey,
      };
    } catch (error) {
      console.error("[OutfitCollectionDao][listOutfitCollections] Error:", error);
      throw error;
    }
  }

  async updateOutfitCollection(
    userId: string,
    outfitCollectionId: string,
    updates: UpdateOutfitCollectionDaoInput
  ): Promise<OutfitCollection> {
    console.log("[OutfitCollectionDao][updateOutfitCollection] Updating:", {
      userId,
      outfitCollectionId,
      updates,
    });

    try {
      const updateExpressions: string[] = [];
      const expressionAttributeValues: any = {};
      const expressionAttributeNames: any = {};

      Object.entries(updates).forEach(([key, value]) => {
        if (value !== undefined) {
          updateExpressions.push(`#${key} = :${key}`);
          expressionAttributeNames[`#${key}`] = key;
          expressionAttributeValues[`:${key}`] = value;
        }
      });

      const { Attributes } = await docClient.send(
        new UpdateCommand({
          TableName: COLLECTION_TABLE,
          Key: { userId, outfitCollectionId },
          UpdateExpression: `set ${updateExpressions.join(", ")}`,
          ExpressionAttributeNames: expressionAttributeNames,
          ExpressionAttributeValues: expressionAttributeValues,
          ReturnValues: "ALL_NEW",
        })
      );

      return Attributes as OutfitCollection;
    } catch (error) {
      console.error("[OutfitCollectionDao][updateOutfitCollection] Error:", error);
      throw error;
    }
  }

  async deleteOutfitCollection(userId: string, outfitCollectionId: string): Promise<void> {
    console.log("[OutfitCollectionDao][deleteOutfitCollection] Deleting:", {
      userId,
      outfitCollectionId,
    });

    try {
      await docClient.send(
        new DeleteCommand({
          TableName: COLLECTION_TABLE,
          Key: { userId, outfitCollectionId },
        })
      );
      console.log("[OutfitCollectionDao][deleteOutfitCollection] Deleted successfully");
    } catch (error) {
      console.error("[OutfitCollectionDao][deleteOutfitCollection] Error:", error);
      throw error;
    }
  }

  async removeOutfitFromCollections(
    userId: string, 
    outfitId: string
  ): Promise<void> {
    console.log("[OutfitCollectionDao][removeOutfitFromCollections] Removing outfit:", {
      userId,
      outfitId,
    });
    
    try {
      // First, find all collections containing this outfit
      const { items: collections } = await this.listOutfitCollections(userId);
      
      // Filter collections that contain the outfit
      const collectionsWithOutfit = collections.filter(
        collection => collection.outfitPointers.includes(outfitId)
      );
      
      // Update each collection to remove the outfit
      const updatePromises = collectionsWithOutfit.map(collection => {
        const updatedPointers = collection.outfitPointers.filter(
          pointer => pointer !== outfitId
        );
        
        return this.updateOutfitCollection(
          userId,
          collection.outfitCollectionId,
          { outfitPointers: updatedPointers }
        );
      });
      
      await Promise.all(updatePromises);
      
      console.log("[OutfitCollectionDao][removeOutfitFromCollections] Removed outfit from all collections successfully");
    } catch (error) {
      console.error("[OutfitCollectionDao][removeOutfitFromCollections] Error:", error);
      throw error;
    }
  }

  async getWeeklyOutfitCollection(
    userId: string
  ): Promise<WeeklyOutfitCollection | null> {
    console.log("[OutfitCollectionDao][getWeeklyOutfitCollection] Getting for user:", userId);

    try {
      const { Item } = await docClient.send(
        new GetCommand({
          TableName: COLLECTION_TABLE,
          Key: { userId, outfitCollectionId: "weekly-outfits" },
        })
      );

      return (Item as WeeklyOutfitCollection) || null;
    } catch (error) {
      console.error("[OutfitCollectionDao][getWeeklyOutfitCollection] Error:", error);
      throw error;
    }
  }

  async createWeeklyOutfitCollection(
    weeklyCollection: WeeklyOutfitCollection
  ): Promise<WeeklyOutfitCollection> {
    console.log("[OutfitCollectionDao][createWeeklyOutfitCollection] Creating:", weeklyCollection);

    try {
      await docClient.send(
        new PutCommand({
          TableName: COLLECTION_TABLE,
          Item: weeklyCollection,
        })
      );

      console.log("[OutfitCollectionDao][createWeeklyOutfitCollection] Created successfully:", weeklyCollection);
      return weeklyCollection;
    } catch (error) {
      console.error("[OutfitCollectionDao][createWeeklyOutfitCollection] Error:", error);
      throw error;
    }
  }

  async updateWeeklyOutfitCollection(
    userId: string,
    updates: UpdateWeeklyOutfitCollectionDaoInput
  ): Promise<WeeklyOutfitCollection> {
    console.log("[OutfitCollectionDao][updateWeeklyOutfitCollection] Updating:", {
      userId,
      updates,
    });

    try {
      const updateExpressions: string[] = [];
      const expressionAttributeValues: any = {};
      const expressionAttributeNames: any = {};

      Object.entries(updates).forEach(([key, value]) => {
        if (value !== undefined) {
          updateExpressions.push(`#${key} = :${key}`);
          expressionAttributeNames[`#${key}`] = key;
          expressionAttributeValues[`:${key}`] = value;
        }
      });

      const { Attributes } = await docClient.send(
        new UpdateCommand({
          TableName: COLLECTION_TABLE,
          Key: { userId, outfitCollectionId: "weekly-outfits" },
          UpdateExpression: `set ${updateExpressions.join(", ")}`,
          ExpressionAttributeNames: expressionAttributeNames,
          ExpressionAttributeValues: expressionAttributeValues,
          ReturnValues: "ALL_NEW",
        })
      );

      return Attributes as WeeklyOutfitCollection;
    } catch (error) {
      console.error("[OutfitCollectionDao][updateWeeklyOutfitCollection] Error:", error);
      throw error;
    }
  }
}