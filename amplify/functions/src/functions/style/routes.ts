import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { StyleError } from "./utils/errors";
import { analyzeUserSelfieController } from "./controllers/analyzeUserSelfieController";
import { getPromptsController } from "./controllers/getPromptsController";
import { generateStyleGuideController } from "./controllers/generateStyleGuideController";
import { createStyleProfileController } from "./controllers/createStyleProfileController";
import { updateStyleProfileController } from "./controllers/updateStyleProfileController";
import { getStyleProfileController } from "./controllers/getStyleProfileController";

export const handleRoutes = async (
  event: APIGatewayProxyEvent,
  context: any
): Promise<APIGatewayProxyResult> => {
  // Extract userId from query parameters
  const userId = event.queryStringParameters?.userId;

  // Handle special endpoints first
  if (event.path.endsWith("/analyze-user-selfie")) {
    return await analyzeUserSelfieController(event);
  }

  if (event.path.endsWith("/get-prompts")) {
    return await getPromptsController(event);
  }

  if (event.path.endsWith("/generate-style-guide")) {
    return await generateStyleGuideController(event);
  }

  // Handle CRUD operations
  switch (event.httpMethod) {
    case "POST":
      return await createStyleProfileController(event);

    case "PUT":
      if (!userId) {
        throw new StyleError(
          "userId is required as a query parameter",
          400,
          "BAD_REQUEST"
        );
      }
      return await updateStyleProfileController(event, userId);

    case "GET":
      if (!userId) {
        throw new StyleError(
          "userId is required as a query parameter.",
          400,
          "BAD_REQUEST"
        );
      }
      return await getStyleProfileController(userId);

    default:
      throw new StyleError(
        `Method ${event.httpMethod} not supported`,
        405,
        "METHOD_NOT_ALLOWED"
      );
  }
}; 