import { APIGatewayProxyEvent } from "aws-lambda";
import { AxiosClient } from "../../../config/axiosClient";
import AUTH_ENDPOINT from "../../../config/constants";
import { StyleProfileUnauthorizedError } from "../utils/errors";

const authClient = AxiosClient.getInstance(AUTH_ENDPOINT).getClient();

const verifyAuthToken = async (token: string): Promise<boolean> => {
  console.log("[Auth][VerifyAuthToken] Verifying auth token");
  try {
    const response = await authClient.post(
      "/auth/token/verify",
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.status === 200;
  } catch (error) {
    console.error(
      "[Auth][VerifyAuthToken] Token verification failed:",
      error
    );
    return false;
  }
};

const isProtectedRoute = (path: string): boolean => {
  const publicRoutes = [
    { path: "/get-prompts" },
    { path: "/analyze-image" },
    { path: "/generate-style-guide" },
  ];

  return !publicRoutes.some((route) => path.endsWith(route.path));
};

export const authorizeRequest = async (event: APIGatewayProxyEvent): Promise<void> => {
  if (!isProtectedRoute(event.path)) {
    console.log(
      "[Auth][AuthorizeRequest] Skipping auth for public route:",
      event.path
    );
    return; // Skip auth for public routes
  }

  const authHeader =
    event.headers?.Authorization || event.headers?.authorization;
  if (!authHeader) {
    console.error("[Auth][AuthorizeRequest] Authorization header is missing");
    throw new StyleProfileUnauthorizedError("Authorization header is required");
  }

  const token = authHeader.replace("Bearer ", "");
  console.log("[Auth][AuthorizeRequest] Token received:", token);

  // Allow SYSTEM as a valid token
  if (token === "SYSTEM") {
    console.log("[Auth][AuthorizeRequest] Access granted for SYSTEM token");
    return;
  }

  const isValid = await verifyAuthToken(token);
  console.log("[Auth][AuthorizeRequest] Token validity check result:", isValid);
  if (!isValid) {
    console.error("[Auth][AuthorizeRequest] Invalid or expired token");
    throw new StyleProfileUnauthorizedError("Invalid or expired token");
  }
}; 