import { APIGatewayProxyResult } from "aws-lambda";

export interface ResponseBody {
  data?: any;
  message?: string;
  errorCode?: string;
  timestamp: string;
  requestId?: string;
}

export const formatResponse = (
  statusCode: number,
  body: ResponseBody,
  additionalHeaders: Record<string, string> = {}
): APIGatewayProxyResult => ({
  statusCode,
  headers: {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "OPTIONS,POST,GET,PUT,DELETE",
    "Access-Control-Allow-Headers":
      "Content-Type,Authorization,X-Amz-Date,X-Api-Key",
    ...additionalHeaders,
  },
  body: JSON.stringify(body),
});

export const handleServiceError = (
  error: unknown,
  context: any
): APIGatewayProxyResult => {
  if (error instanceof Error) {
    const statusCode = (error as any).statusCode || 500;
    const errorCode = (error as any).errorCode || "INTERNAL_ERROR";
    
    return formatResponse(statusCode, {
      message: error.message,
      errorCode,
      timestamp: new Date().toISOString(),
      requestId: context.awsRequestId,
    });
  }

  console.error("[Handler] Unexpected error:", {
    error,
    errorType: error instanceof Error ? error.constructor.name : typeof error,
    errorMessage: error instanceof Error ? error.message : String(error),
    requestId: context.awsRequestId,
  });

  return formatResponse(500, {
    message: "An unexpected error occurred",
    errorCode: "INTERNAL_ERROR",
    timestamp: new Date().toISOString(),
    requestId: context.awsRequestId,
  });
}; 