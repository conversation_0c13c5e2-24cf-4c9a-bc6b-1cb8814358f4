export class StyleError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public errorCode: string
  ) {
    super(message);
    this.name = "StyleError";
  }
}

export class StyleProfileServiceError extends StyleError {
  constructor(message: string, errorCode: string, statusCode: number) {
    super(message, statusCode, errorCode);
    this.name = "StyleProfileServiceError";
  }
}

export class StyleProfileValidationError extends StyleError {
  constructor(message: string) {
    super(message, 400, "VALIDATION_ERROR");
    this.name = "StyleProfileValidationError";
  }
}

export class StyleProfileNotFoundError extends StyleError {
  constructor(message: string = "Style profile not found") {
    super(message, 404, "NOT_FOUND");
    this.name = "StyleProfileNotFoundError";
  }
}

export class StyleProfileUnauthorizedError extends StyleError {
  constructor(message: string = "Unauthorized access") {
    super(message, 401, "UNAUTHORIZED");
    this.name = "StyleProfileUnauthorizedError";
  }
} 