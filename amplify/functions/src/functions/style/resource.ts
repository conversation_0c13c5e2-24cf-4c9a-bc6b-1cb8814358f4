import 'dotenv/config'; // This must be the first import
import { defineFunction } from "@aws-amplify/backend";

// Log for debugging
console.log("[Resource] Env Variable:", process.env.ENV_VARIABLE);

export const styleProfileController = defineFunction({
  name: "styleProfileController",
  entry: "./handler.ts",
  timeoutSeconds: 300,
  environment: {
    ENV_VARIABLE: process.env.ENV_VARIABLE ?? "default",
  },
});