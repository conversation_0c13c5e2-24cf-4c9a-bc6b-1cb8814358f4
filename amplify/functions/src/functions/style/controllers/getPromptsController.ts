import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { StyleProfileService } from "../../../services/styleProfileService";
import { formatResponse } from "../utils/responseFormatter";
import { StyleProfileValidationError } from "../utils/errors";

export const getPromptsController = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    const styleProfileService = new StyleProfileService();
    const prompts = await styleProfileService.getPrompts();

    return formatResponse(200, {
      data: prompts,
      message: "Prompts retrieved successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("[GetPromptsController] Error:", error);
    throw error;
  }
}; 