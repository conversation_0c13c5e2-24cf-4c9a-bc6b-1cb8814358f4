import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { StyleProfileService } from "../../../services/styleProfileService";
import { CreateStyleProfileInput } from "../../../types/style.types";
import { formatResponse } from "../utils/responseFormatter";
import { StyleProfileValidationError } from "../utils/errors";

export const generateStyleGuideController = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    if (!event.body) {
      throw new StyleProfileValidationError("Request body is required");
    }

    let input: CreateStyleProfileInput;
    try {
      input = JSON.parse(event.body);
    } catch (e) {
      throw new StyleProfileValidationError("Invalid JSON in request body");
    }

    // Validate required fields
    if (!input.userGender || !input.userBodyType || !input.userHeight || !input.userSeason) {
      throw new StyleProfileValidationError("Missing required fields");
    }

    const styleProfileService = new StyleProfileService();
    const styleGuide = await styleProfileService.generateStyleGuide(input);

    return formatResponse(200, {
      data: styleGuide,
      message: "Style guide generated successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("[GenerateStyleGuideController] Error:", error);
    throw error;
  }
}; 