import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { StyleProfileService } from "../../../services/styleProfileService";
import { UpdateStyleProfileInput } from "../../../types/style.types";
import { formatResponse } from "../utils/responseFormatter";
import { StyleProfileValidationError, StyleProfileNotFoundError } from "../utils/errors";

export const updateStyleProfileController = async (
  event: APIGatewayProxyEvent,
  styleProfileId: string
): Promise<APIGatewayProxyResult> => {
  try {
    if (!event.body) {
      throw new StyleProfileValidationError("Request body is required");
    }

    let input: UpdateStyleProfileInput;
    try {
      input = JSON.parse(event.body);
    } catch (e) {
      throw new StyleProfileValidationError("Invalid JSON in request body");
    }

    // Validate that at least one field is being updated
    if (!Object.keys(input).length) {
      throw new StyleProfileValidationError("No fields to update");
    }

    const styleProfileService = new StyleProfileService();
    const styleProfile = await styleProfileService.updateProfile(styleProfileId, input);

    if (!styleProfile) {
      throw new StyleProfileNotFoundError();
    }

    return formatResponse(200, {
      data: styleProfile,
      message: "Style profile updated successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("[UpdateStyleProfileController] Error:", error);
    throw error;
  }
}; 