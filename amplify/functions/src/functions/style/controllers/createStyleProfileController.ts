import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { StyleProfileService } from "../../../services/styleProfileService";
import { CreateStyleProfileInput } from "../../../types/style.types";
import { formatResponse } from "../utils/responseFormatter";
import { StyleProfileValidationError } from "../utils/errors";

export const createStyleProfileController = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    if (!event.body) {
      throw new StyleProfileValidationError("Request body is required");
    }

    let input: CreateStyleProfileInput;
    try {
      input = JSON.parse(event.body);
    } catch (e) {
      throw new StyleProfileValidationError("Invalid JSON in request body");
    }

    // Validate required fields
    if (!input.userId || !input.userGender || !input.userBodyType || !input.userHeight || 
       !input.userEyeColor || !input.userHairColor || 
        !input.userContrast || !input.userSeason || !input.userProfileImageUrl) {
      throw new StyleProfileValidationError("Missing required fields");
    }

    const styleProfileService = new StyleProfileService();
    const styleProfile = await styleProfileService.createProfile(input);

    return formatResponse(201, {
      data: styleProfile,
      message: "Style profile created successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("[CreateStyleProfileController] Error:", error);
    throw error;
  }
}; 