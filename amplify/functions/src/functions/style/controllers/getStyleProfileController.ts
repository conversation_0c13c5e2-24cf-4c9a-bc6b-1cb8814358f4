import { APIGatewayProxyResult } from "aws-lambda";
import { StyleProfileService } from "../../../services/styleProfileService";
import { formatResponse } from "../utils/responseFormatter";
import { StyleProfileNotFoundError } from "../utils/errors";

export const getStyleProfileController = async (
  styleProfileId: string
): Promise<APIGatewayProxyResult> => {
  try {
    const styleProfileService = new StyleProfileService();
    const styleProfile = await styleProfileService.getProfile(styleProfileId);

    if (!styleProfile) {
      throw new StyleProfileNotFoundError();
    }

    return formatResponse(200, {
      data: styleProfile,
      message: "Style profile retrieved successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("[GetStyleProfileController] Error:", error);
    throw error;
  }
}; 