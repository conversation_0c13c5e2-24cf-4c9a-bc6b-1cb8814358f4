import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { StyleProfileService } from "../../../services/styleProfileService";
import { formatResponse } from "../utils/responseFormatter";
import { StyleProfileValidationError } from "../utils/errors";
import { Undertone } from "../../../types/style.types";

export const analyzeUserSelfieController = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    if (!event.body) {
      throw new StyleProfileValidationError("Request body is required");
    }

    let input: { imageUrl: string; userUndertone: Undertone };
    try {
      input = JSON.parse(event.body);
    } catch (e) {
      throw new StyleProfileValidationError("Invalid JSON in request body");
    }

    // Validate required fields
    if (!input.imageUrl || !input.userUndertone) {
      throw new StyleProfileValidationError("Missing required fields");
    }

    const styleProfileService = new StyleProfileService();
    const analysis = await styleProfileService.analyzeUserSelfie(input.imageUrl, input.userUndertone);

    return formatResponse(200, {
      data: analysis,
      message: "Image analyzed successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("[AnalyzeUserSelfieController] Error:", error);
    throw error;
  }
}; 