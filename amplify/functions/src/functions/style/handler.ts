import { APIGatewayProxyHand<PERSON> } from "aws-lambda";
import { handleRoutes } from "./routes";
import { authorizeRequest } from "./middleware/auth";
import { handleServiceError } from "./utils/responseFormatter";

export const handler: APIGatewayProxyHandler = async (event, context) => {
  // Track start time for performance monitoring
  const startTime = Date.now();

  try {
    // Log request details
    console.log("[Handler] Request received:", {
      path: event.path,
      method: event.httpMethod,
      queryParams: event.queryStringParameters,
      hasBody: !!event.body,
      requestId: context.awsRequestId,
      functionName: context.functionName,
      remaining: context.getRemainingTimeInMillis(),
    });

    // Handle CORS preflight requests
    if (event.httpMethod === "OPTIONS") {
      console.log("[Handler] Handling CORS preflight request");
      return {
        statusCode: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "OPTIONS,POST,GET,PUT,DELETE",
          "Access-Control-Allow-Headers":
            "Content-Type,Authorization,X-Amz-Date,X-Api-Key",
        },
        body: JSON.stringify({
          message: "CORS preflight successful",
          timestamp: new Date().toISOString(),
        }),
      };
    }

    // Authorize request
   // await authorizeRequest(event);

    // Handle the actual request
    const response = await handleRoutes(event, context);

    // Log response information
    console.log("[Handler] Response generated:", {
      statusCode: response.statusCode,
      path: event.path,
      method: event.httpMethod,
      requestId: context.awsRequestId,
      executionTime: `${Date.now() - startTime}ms`,
    });

    return response;
  } catch (error) {
    // Log the error
    console.error("[Handler] Unhandled error:", {
      error,
      errorType: error instanceof Error ? error.constructor.name : typeof error,
      errorMessage: error instanceof Error ? error.message : String(error),
      path: event.path,
      method: event.httpMethod,
      requestId: context.awsRequestId,
      executionTime: `${Date.now() - startTime}ms`,
    });

    return handleServiceError(error, context);
  }
}; 