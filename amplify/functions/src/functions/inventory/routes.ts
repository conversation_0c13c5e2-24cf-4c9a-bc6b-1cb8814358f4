import { APIGatewayProxyEvent } from "aws-lambda";
import { apparelSearchController } from "./controllers/apparelSearchController";
import { apparelSuggestionsController } from "./controllers/apparelSuggestionsController";

export const routes = {
  "GET /apparels/search": async (event: APIGatewayProxyEvent) => {
    return apparelSearchController.searchApparels(event);
  },
  
  "GET /apparels/suggestions": async (event: APIGatewayProxyEvent) => {
    return apparelSuggestionsController.getSuggestions(event);
  }
}; 