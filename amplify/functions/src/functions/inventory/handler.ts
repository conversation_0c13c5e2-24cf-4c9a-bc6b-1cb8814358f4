import { APIGatewayProxyEvent } from "aws-lambda";
import { routes } from "./routes";

type RouteKey = keyof typeof routes;

export const handler = async (event: APIGatewayProxyEvent) => {
  const requestId = event.requestContext.requestId;

  console.log(`[<PERSON><PERSON>][${requestId}] Processing request:`, {
    method: event.httpMethod,
    path: event.path,
    queryParams: event.queryStringParameters,
    hasBody: !!event.body,
  });

  // Handle CORS preflight requests
  if (event.httpMethod === "OPTIONS") {
    console.log(`[Handler][${requestId}] Handling CORS preflight request`);
    return {
      statusCode: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "OPTIONS,POST,GET,PUT,DELETE",
        "Access-Control-Allow-Headers":
          "Content-Type,Authorization,X-Amz-Date,X-Api-Key",
      },
      body: JSON.stringify({
        message: "CORS preflight successful",
        timestamp: new Date().toISOString(),
      }),
    };
  }

  try {
    const route = `${event.httpMethod} ${event.path}` as RouteKey;
    console.log(`[Handler][${requestId}] Processing route: ${route}`);

    const routeHandler = routes[route];
    if (!routeHandler) {
      console.warn(`[Handler][${requestId}] Route not found: ${route}`);
      return {
        statusCode: 404,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "OPTIONS,POST,GET,PUT,DELETE",
          "Access-Control-Allow-Headers":
            "Content-Type,Authorization,X-Amz-Date,X-Api-Key",
        },
        body: JSON.stringify({
          message: "Route not found",
          code: "NOT_FOUND",
          path: event.path,
          method: event.httpMethod,
        }),
      };
    }

    return await routeHandler(event);
  } catch (error) {
    console.error(`[Handler][${requestId}] Unhandled error:`, {
      error,
      errorMessage: error instanceof Error ? error.message : "Unknown error",
      errorStack: error instanceof Error ? error.stack : undefined,
      path: event.path,
      method: event.httpMethod,
    });

    // Handle specific known errors
    if (error instanceof Error) {
      if (error.message.includes("Index not found")) {
        return {
          statusCode: 503,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "OPTIONS,POST,GET,PUT,DELETE",
            "Access-Control-Allow-Headers":
              "Content-Type,Authorization,X-Amz-Date,X-Api-Key",
          },
          body: JSON.stringify({
            message: "Search service temporarily unavailable",
            code: "SERVICE_UNAVAILABLE",
          }),
        };
      }

      if (error.message.includes("validation")) {
        return {
          statusCode: 400,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "OPTIONS,POST,GET,PUT,DELETE",
            "Access-Control-Allow-Headers":
              "Content-Type,Authorization,X-Amz-Date,X-Api-Key",
          },
          body: JSON.stringify({
            message: error.message,
            code: "VALIDATION_ERROR",
          }),
        };
      }
    }

    // Generic error response
    return {
      statusCode: 500,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "OPTIONS,POST,GET,PUT,DELETE",
        "Access-Control-Allow-Headers":
          "Content-Type,Authorization,X-Amz-Date,X-Api-Key",
      },
      body: JSON.stringify({
        message: "An unexpected error occurred",
        code: "INTERNAL_ERROR",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
    };
  }
};