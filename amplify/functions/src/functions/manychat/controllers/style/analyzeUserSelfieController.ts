import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { ManyChatError, formatResponse } from "../../utils/errors";
import { AnalyzeUserSelfieRequest } from "../../../../types/manychat";
import { manychatStyleService } from "../../../../services/manychat";

export const analyzeUserSelfie = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    if (!event.body) {
      throw new ManyChatError(
        "Request body is required",
        400,
        "MISSING_REQUEST_BODY"
      );
    }

    let input: AnalyzeUserSelfieRequest;
    try {
      input = JSON.parse(event.body);
    } catch (e) {
      throw new ManyChatError(
        "Invalid JSON in request body",
        400,
        "INVALID_JSON"
      );
    }

    // Validate required fields
    if (!input.imageUrl || !input.userUndertone || !input.userId) {
      throw new ManyChatError(
        "Missing required fields: imageUrl, userUndertone, and userId",
        400,
        "MISSING_REQUIRED_FIELDS"
      );
    }

    console.log("[AnalyzeUserSelfieController] Processing request:", {
      userId: input.userId,
      userUndertone: input.userUndertone,
      imageUrl: input.imageUrl.substring(0, 30) + "..." // Log partial URL for privacy
    });

    // Call the service method to analyze the selfie
    const analysis = await manychatStyleService.analyzeUserSelfie(
      input.userId,
      input.imageUrl,
      input.userUndertone
    );

    return formatResponse(200, {
      data: analysis,
      message: "Image analyzed successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("[AnalyzeUserSelfieController] Error:", error);

    // Handle specific errors
    if (error instanceof ManyChatError) {
      return formatResponse(
        error.statusCode,
        {
          data: {
            isValid: false,
            errorMessage: error.message
          },
          message: error.message
        },
        error.errorCode
      );
    }

    // Handle generic errors
    return formatResponse(
      500,
      {
        data: {
          isValid: false,
          errorMessage: "Failed to analyze image due to an internal error"
        },
        message: "Internal server error"
      },
      "INTERNAL_SERVER_ERROR"
    );
  }
};
