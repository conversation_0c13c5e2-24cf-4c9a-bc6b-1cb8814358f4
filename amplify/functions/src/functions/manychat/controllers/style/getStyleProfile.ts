import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { ManyChatError, formatResponse } from "../../utils/errors";
import { manychatCommonService, manychatStyleService } from "../../../../services/manychat";
import { MANYCHAT_FIELD_IDS, MANYCHAT_STATUS, MANYCHAT_BOOLEAN } from "../../../../external/manychat/constants";

export const getStyleProfile = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    // Extract userId from query parameters
    const userId = event.queryStringParameters?.userId;
    const manychatSubscriberId = event.queryStringParameters?.manychatSubscriberId;

    if (!userId) {
      console.error("[GetStyleProfileController] Missing userId parameter");

      // Set STYLE_PROFILE_EXISTS to FALSE in Manychat if subscriberId is provided
      if (manychatSubscriberId) {
        await manychatCommonService.setCustomFields(Number(manychatSubscriberId), [
          {
            field_id: MANYCHAT_FIELD_IDS.STYLE_PROFILE_EXISTS,
            field_value: MANYCHAT_BOOLEAN.FALSE
          }
        ]);
      }

      // Return 200 with empty data for Manychat compatibility
      return formatResponse(200, {
        data: null,
        message: "userId is required as a query parameter",
        styleProfileExists: MANYCHAT_BOOLEAN.FALSE,
        timestamp: new Date().toISOString(),
      });
    }

    console.log("[GetStyleProfileController] Processing request:", {
      userId,
      manychatSubscriberId
    });

    try {
      // Call the service method to get the style profile
      const profile = await manychatStyleService.getStyleProfile(userId);

      // If profile exists, set STYLE_PROFILE_EXISTS to TRUE in Manychat
      if (profile && manychatSubscriberId) {
        await manychatCommonService.setCustomFields(Number(manychatSubscriberId), [
          {
            field_id: MANYCHAT_FIELD_IDS.STYLE_PROFILE_EXISTS,
            field_value: MANYCHAT_BOOLEAN.TRUE
          }
        ]);

        // Remove styleGuide from profile as it's not needed for Manychat
        const { styleGuide, ...profileWithoutStyleGuide } = profile;

        return formatResponse(200, {
          data: profileWithoutStyleGuide,
          message: "Style profile retrieved successfully",
          styleProfileExists: MANYCHAT_BOOLEAN.TRUE,
          timestamp: new Date().toISOString(),
        });
      }

      // If profile doesn't exist, set STYLE_PROFILE_EXISTS to FALSE in Manychat
      if (manychatSubscriberId) {
        await manychatCommonService.setCustomFields(Number(manychatSubscriberId), [
          {
            field_id: MANYCHAT_FIELD_IDS.STYLE_PROFILE_EXISTS,
            field_value: MANYCHAT_BOOLEAN.FALSE
          }
        ]);
      }

      // Return 200 with empty data for Manychat compatibility
      return formatResponse(200, {
        data: null,
        message: `Style profile not found for user: ${userId}`,
        styleProfileExists: MANYCHAT_BOOLEAN.FALSE,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error("[GetStyleProfileController] Error retrieving style profile:", error);

      // Set STYLE_PROFILE_EXISTS to FALSE in Manychat if subscriberId is provided
      if (manychatSubscriberId) {
        try {
          await manychatCommonService.setCustomFields(Number(manychatSubscriberId), [
            {
              field_id: MANYCHAT_FIELD_IDS.STYLE_PROFILE_EXISTS,
              field_value: MANYCHAT_BOOLEAN.FALSE
            }
          ]);
        } catch (setFieldError) {
          console.error("[GetStyleProfileController] Error setting Manychat field:", setFieldError);
        }
      }

      // Return 200 with empty data for Manychat compatibility
      return formatResponse(200, {
        data: null,
        message: "Failed to retrieve style profile",
        styleProfileExists: MANYCHAT_BOOLEAN.FALSE,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString(),
      });
    }
  } catch (error) {
    console.error("[GetStyleProfileController] Unexpected error:", error);

    // Return 200 with empty data for Manychat compatibility
    return formatResponse(200, {
      data: null,
      message: "An unexpected error occurred",
      styleProfileExists: MANYCHAT_BOOLEAN.FALSE,
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString(),
    });
  }
};
