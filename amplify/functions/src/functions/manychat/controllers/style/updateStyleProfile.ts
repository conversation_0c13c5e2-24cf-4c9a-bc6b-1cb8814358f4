import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { formatResponse } from "../../utils/errors";
import { ManychatUpdateStyleProfileRequest, ManychatResponse } from "../../../../types/manychat";
import { manychatStyleService } from "../../../../services/manychat";
import { manychatCommonService } from "../../../../services/manychat";
import { MANYCHAT_FIELD_IDS, MANYCHAT_STATUS, MANYCHAT_BOOLEAN } from "../../../../external/manychat/constants";
import { isValidUrl } from "../../../../utils/urlUtil";

export const updateStyleProfile = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  // Initialize response object
  const response: ManychatResponse = {
    status: MANYCHAT_STATUS.FAILURE,
    message: "",
    timestamp: new Date().toISOString()
  };

  try {
    if (!event.body) {
      response.message = "Request body is required";
      response.errorMessage = "MISSING_REQUEST_BODY";
      return formatResponse(200, response);
    }

    let input: ManychatUpdateStyleProfileRequest;
    try {
      input = JSON.parse(event.body);
    } catch (e) {
      response.message = "Invalid JSON in request body";
      response.errorMessage = "INVALID_JSON";
      return formatResponse(200, response);
    }

    // Validate userId is present
    if (!input.userId) {
      response.message = "userId is required";
      response.errorMessage = "MISSING_REQUIRED_FIELDS";
      return formatResponse(200, response);
    }

    // Filter out null or empty values
    const filteredInput: ManychatUpdateStyleProfileRequest = { userId: input.userId };

    // Preserve manychatSubscriberId if it exists
    if (input.manychatSubscriberId) {
      filteredInput.manychatSubscriberId = input.manychatSubscriberId;
    }

    // Validate profile image URL if provided
    if (input.userProfileImageUrl) {
      const urlValidationResult = isValidUrl(input.userProfileImageUrl);
      if (!urlValidationResult.valid) {
        console.log(`[updateStyleProfile] Invalid profile image URL: ${urlValidationResult.error}. Treating as missing.`);
        // Remove the invalid URL from the input object completely
        delete input.userProfileImageUrl;
      } else {
        // If selfie URL is valid, undertone is required for selfie analysis
        if (!input.userUndertone) {
          console.log(`[updateStyleProfile] Missing userUndertone which is required for selfie analysis. Rejecting request.`);
          response.message = "userUndertone is required when providing a profile image";
          response.errorMessage = "MISSING_REQUIRED_FIELDS";
          return formatResponse(200, response);
        }
        console.log(`[updateStyleProfile] Valid profile image URL and undertone provided. Will attempt selfie analysis.`);
      }
    }

    // Only include fields with non-null, non-empty values
    Object.entries(input).forEach(([key, value]) => {
      if (key !== 'userId' && key !== 'manychatSubscriberId' && value !== null && value !== undefined && value !== '') {
        if (key in filteredInput || typeof key === 'string') {
          (filteredInput as Record<string, any>)[key] = value;
        }
      }
    });

    // Ensure at least one field to update is provided
    const updateFields = Object.keys(filteredInput).filter(key => key !== "userId" && key !== "manychatSubscriberId");
    if (updateFields.length === 0) {
      response.message = "At least one field to update is required";
      response.errorMessage = "NO_UPDATE_FIELDS";
      return formatResponse(200, response);
    }

    console.log("[UpdateStyleProfileController] Processing request:", {
      userId: filteredInput.userId,
      updateFields: updateFields.join(", "),
      hasProfileImage: !!filteredInput.userProfileImageUrl
    });

    // Extract manychatSubscriberId if provided
    const manychatSubscriberId = filteredInput.manychatSubscriberId ? Number(filteredInput.manychatSubscriberId) : null;

    try {
      // Call the service method to update the style profile
      const profile = await manychatStyleService.updateStyleProfile(filteredInput);

      // Update Manychat field if subscriberId is provided
      if (manychatSubscriberId) {
        await manychatCommonService.setCustomFields(manychatSubscriberId, [
          {
            field_id: MANYCHAT_FIELD_IDS.STYLE_PROFILE_EXISTS,
            field_value: MANYCHAT_BOOLEAN.TRUE
          }
        ]);
      }

      // Set success response
      response.status = MANYCHAT_STATUS.SUCCESS;
      response.message = "Style profile updated successfully";
      response.data = profile;

      return formatResponse(200, response);
    } catch (serviceError) {
      console.error("[UpdateStyleProfileController] Service error:", serviceError);

      // Handle profile not found case
      if (serviceError instanceof Error && serviceError.message.includes("Profile not found")) {
        response.message = serviceError.message;
        response.errorMessage = "PROFILE_NOT_FOUND";

        // Update Manychat field if subscriberId is provided
        if (manychatSubscriberId) {
          try {
            await manychatCommonService.setCustomFields(manychatSubscriberId, [
              {
                field_id: MANYCHAT_FIELD_IDS.STYLE_PROFILE_EXISTS,
                field_value: MANYCHAT_BOOLEAN.FALSE
              }
            ]);
          } catch (manychatError) {
            console.error("[UpdateStyleProfileController] Failed to update Manychat field:", manychatError);
          }
        }

        return formatResponse(200, response);
      }

      // Handle other service errors
      response.message = "Failed to update style profile";
      response.errorMessage = serviceError instanceof Error ? serviceError.message : String(serviceError);

      return formatResponse(200, response);
    }
  } catch (error) {
    console.error("[UpdateStyleProfileController] Unexpected error:", error);

    // Handle unexpected errors
    response.message = "An unexpected error occurred";
    response.errorMessage = error instanceof Error ? error.message : String(error);

    return formatResponse(200, response);
  }
};
