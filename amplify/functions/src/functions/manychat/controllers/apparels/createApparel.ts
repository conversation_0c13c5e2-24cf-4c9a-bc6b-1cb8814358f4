import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { ManyChatError, formatResponse } from "../../utils/errors";
import { manychatApparelService } from "../../../../services/manychat";
import { ApparelProfile } from "../../../../types/apparel.types";
import { ManychatCreateApparelRequest } from "../../../../types/manychat";
import { isValidUrl } from "../../../../utils/urlUtil";
import { ImageStorageService } from "../../../../services/imageStorageService";

export const createApparel = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    console.log("[createApparel] Controller started");

    if (!event.body) {
      throw new ManyChatError(
        "Request body is required",
        400,
        "MISSING_REQUEST_BODY"
      );
    }

    let requestBody: any;
    try {
      requestBody = JSON.parse(event.body);
    } catch (e) {
      throw new ManyChatError(
        "Invalid JSON in request body",
        400,
        "INVALID_JSON"
      );
    }
    // Extract required parameters
    const { imageUrls, userId, gender, manychatSubscriberId } = requestBody;

    // Validate required fields
    if (!imageUrls || !Array.isArray(imageUrls) || imageUrls.length === 0) {
      throw new ManyChatError(
        "Missing required field: imageUrls must be a non-empty array",
        400,
        "MISSING_REQUIRED_FIELD"
      );
    }

    // Validate and filter image URLs
    const validImageUrls = imageUrls.filter(url => {
      const validationResult = isValidUrl(url);
      if (!validationResult.valid) {
        console.log(`[createApparel] Invalid image URL: ${url.substring(0, 30)}... - ${validationResult.error}`);
      }
      return validationResult.valid;
    });

    // If no valid URLs remain after filtering, throw an error
    if (validImageUrls.length === 0) {
      throw new ManyChatError(
        "No valid image URLs provided. All URLs must be valid and start with http:// or https://",
        400,
        "INVALID_IMAGE_URLS"
      );
    }

    console.log(`[createApparel] Filtered ${imageUrls.length - validImageUrls.length} invalid URLs. Processing ${validImageUrls.length} valid URLs.`);

    if (!userId) {
      throw new ManyChatError(
        "Missing required field: userId",
        400,
        "MISSING_REQUIRED_FIELD"
      );
    }

    // Store all valid images in Azure storage
    console.log(`[createApparel] Storing ${validImageUrls.length} images in Azure storage`);
    const imageStorageService = new ImageStorageService();
    const storedImageUrls: string[] = [];

    // Process each image and store in Azure
    for (const imageUrl of validImageUrls) {
      try {
        // Create a unique path for the original image
        const storagePath = `users/${userId}/original-images/original-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;

        // Upload the image to Azure storage
        const storedImageUrl = await imageStorageService.uploadImage(
          imageUrl,
          storagePath
        );

        storedImageUrls.push(storedImageUrl);
        console.log(`[createApparel] Successfully stored image in Azure: ${imageUrl.substring(0, 30)}...`);
      } catch (error) {
        console.error(`[createApparel] Error storing image in Azure:`, {
          error: error instanceof Error ? error.message : 'Unknown error',
          imageUrl: imageUrl.substring(0, 30) + '...'
        });

        // If we fail to store the image, use the original URL
        // This ensures the flow doesn't break if Azure storage is temporarily unavailable
        storedImageUrls.push(imageUrl);
        console.warn(`[createApparel] Falling back to original URL: ${imageUrl.substring(0, 30)}...`);
      }
    }

    console.log(`[createApparel] Successfully stored ${storedImageUrls.length} images in Azure storage`);

    if (!manychatSubscriberId) {
      throw new ManyChatError(
        "Missing required field: manychatSubscriberId",
        400,
        "MISSING_REQUIRED_FIELD"
      );
    }

    // Normalize gender to uppercase for case-insensitive comparison
    const normalizedGender = gender ? gender.toUpperCase() : null;

    if (!normalizedGender || (normalizedGender !== "MALE" && normalizedGender !== "FEMALE")) {
      throw new ManyChatError(
        "Missing or invalid gender: must be 'MALE' or 'FEMALE'",
        400,
        "INVALID_GENDER"
      );
    }

    console.log("[createApparel] Processing request", {
      userId,
      gender: normalizedGender, // Log the normalized gender value
      originalGender: gender,   // Also log the original gender for debugging
      totalImageCount: imageUrls.length,
      validImageCount: validImageUrls.length
    });

    // Create the request object
    const createRequest: ManychatCreateApparelRequest = {
      userId,
      manychatSubscriberId: Number(manychatSubscriberId), // Ensure it's a number
      gender: normalizedGender as ApparelProfile,
      imageUrls: storedImageUrls // Use the stored Azure URLs
    };

    const result = await manychatApparelService.createApparels(createRequest);

    console.log("[createApparel] Result summary:", {
      success: result.success,
      apparelsCount: result.apparels?.length || 0,
      duplicatesCount: result.duplicates?.length || 0,
      processedImages: result.processedImages,
      successfullyProcessed: result.successfullyProcessed
    });

    // Handle different response scenarios
    if (!result.success) {
      // If duplicates were found, return them with a specific status code
      if (result.duplicates && result.duplicates.length > 0) {
        return formatResponse(409, result);
      }

      // For other failures, return the error message
      return formatResponse(400, result);
    }

    // Success response
    return formatResponse(201, result);
  } catch (error) {
    console.error("[createApparel] Error:", {
      error: error instanceof Error ? error.message : 'Unknown error',
      errorType: error instanceof Error ? error.constructor.name : typeof error
    });

    // Handle specific errors
    if (error instanceof ManyChatError) {
      return formatResponse(
        error.statusCode,
        {
          message: error.message,
          timestamp: new Date().toISOString()
        },
        error.errorCode
      );
    }

    // Handle generic errors
    return formatResponse(
      500,
      {
        message: "Failed to create apparel due to an internal error",
        timestamp: new Date().toISOString()
      },
      "INTERNAL_SERVER_ERROR"
    );
  }
};
