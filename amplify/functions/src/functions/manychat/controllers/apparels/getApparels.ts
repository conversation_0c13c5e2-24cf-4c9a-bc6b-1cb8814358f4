import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { ManyChatError, formatResponse } from "../../utils/errors";
import { manychatApparelService } from "../../../../services/manychat";
import { ApparelCategory } from "../../../../types/apparel.types";
import { ManychatGetApparelsByCategoryRequest } from "../../../../types/manychat";

export const getApparels = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    console.log("[getApparels] Controller started");

    // Extract query parameters
    const queryParams = event.queryStringParameters || {};
    const { userId, manychatSubscriberId, apparelCategory } = queryParams;

    // Validate required fields
    if (!userId) {
      throw new ManyChatError(
        "Missing required query parameter: userId",
        400,
        "MISSING_REQUIRED_FIELD"
      );
    }

    if (!manychatSubscriberId) {
      throw new ManyChatError(
        "Missing required query parameter: manychatSubscriberId",
        400,
        "MISSING_REQUIRED_FIELD"
      );
    }

    // Validate apparelCategory if provided
    if (apparelCategory && !['TOPWEAR', 'BOTTOMWEAR', 'FOOTWEAR'].includes(apparelCategory)) {
      throw new ManyChatError(
        "Invalid apparelCategory: must be 'TOPWEAR', 'BOTTOMWEAR', or 'FOOTWEAR'",
        400,
        "INVALID_APPAREL_CATEGORY"
      );
    }

    console.log("[getApparels] Processing request", {
      userId,
      apparelCategory: apparelCategory || 'all'
    });

    // Create the request object
    const request: ManychatGetApparelsByCategoryRequest = {
      userId,
      manychatSubscriberId: Number(manychatSubscriberId), // Ensure it's a number
      apparelCategory: apparelCategory as ApparelCategory // Optional
    };

    const result = await manychatApparelService.getApparels(request);

    console.log("[getApparels] Result:", {
      success: result.success,
      message: result.message
    });

    // Handle different response scenarios
    if (!result.success) {
      return formatResponse(400, result);
    }

    // Success response
    return formatResponse(200, result);
  } catch (error) {
    console.error("[getApparels] Error:", {
      error: error instanceof Error ? error.message : 'Unknown error',
      errorType: error instanceof Error ? error.constructor.name : typeof error
    });

    // Handle specific errors
    if (error instanceof ManyChatError) {
      return formatResponse(
        error.statusCode,
        {
          message: error.message,
          timestamp: new Date().toISOString()
        },
        error.errorCode
      );
    }

    // Handle generic errors
    return formatResponse(
      500,
      {
        message: "Failed to get apparels due to an internal error",
        timestamp: new Date().toISOString()
      },
      "INTERNAL_SERVER_ERROR"
    );
  }
};
