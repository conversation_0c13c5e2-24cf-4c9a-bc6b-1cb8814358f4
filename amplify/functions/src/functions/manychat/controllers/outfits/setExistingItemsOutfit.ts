import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { ManyChatError, formatResponse } from "../../utils/errors";
import { manychatOutfitService } from "../../../../services/manychat";
import { ManychatSetExistingItemsOutfitRequest, ManychatSituationalContext } from "../../../../types/manychat";
import { OutfitError } from "../../../../types/outfits.types";
import { isValidManychatValue } from "../../utils/validation";

export const setExistingItemsOutfit = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    console.log("[setExistingItemsOutfit] Controller started");

    if (!event.body) {
      throw new ManyChatError(
        "Request body is required",
        400,
        "MISSING_REQUEST_BODY"
      );
    }

    let requestBody: any;
    try {
      requestBody = JSON.parse(event.body);
    } catch (e) {
      throw new ManyChatError(
        "Invalid JSON in request body",
        400,
        "INVALID_JSON"
      );
    }

    // Extract required parameters
    const { userId, manychatSubscriberId, situationContext } = requestBody;

    // Validate required fields
    if (!userId) {
      throw new ManyChatError(
        "Missing required field: userId",
        400,
        "MISSING_REQUIRED_FIELD"
      );
    }

    if (!manychatSubscriberId) {
      throw new ManyChatError(
        "Missing required field: manychatSubscriberId",
        400,
        "MISSING_REQUIRED_FIELD"
      );
    }

    if (!situationContext) {
      throw new ManyChatError(
        "Missing required field: situationContext",
        400,
        "MISSING_REQUIRED_FIELD"
      );
    }

    if (!situationContext.occasion) {
      throw new ManyChatError(
        "Missing required field: situationContext.occasion",
        400,
        "MISSING_REQUIRED_FIELD"
      );
    }

    if (!situationContext.stylePreference) {
      throw new ManyChatError(
        "Missing required field: situationContext.stylePreference",
        400,
        "MISSING_REQUIRED_FIELD"
      );
    }

    console.log("[setExistingItemsOutfit] Processing request", {
      userId,
      manychatSubscriberId,
      situationContext: JSON.stringify(situationContext)
    });

    // Create a filtered situational context that excludes invalid values
    const filteredSituationContext: ManychatSituationalContext = {
      occasion: situationContext.occasion,
      stylePreference: situationContext.stylePreference,
    };

    // Only include location and weather if they are valid values
    if (isValidManychatValue(situationContext.location)) {
      filteredSituationContext.location = situationContext.location;
    } else {
      console.log(`[setExistingItemsOutfit] Skipping invalid location value: "${situationContext.location}"`);
    }

    if (isValidManychatValue(situationContext.feelsLikeWeather)) {
      filteredSituationContext.feelsLikeWeather = situationContext.feelsLikeWeather;
    } else {
      console.log(`[setExistingItemsOutfit] Skipping invalid weather value: "${situationContext.feelsLikeWeather}"`);
    }

    // Create the request object with the filtered context
    const request: ManychatSetExistingItemsOutfitRequest = {
      userId,
      manychatSubscriberId: Number(manychatSubscriberId), // Ensure it's a number
      situationContext: filteredSituationContext
    };

    // Call the service method
    await manychatOutfitService.setExistingItemsOutfit(request);

    // Return success response
    return formatResponse(200, {
      success: true,
      message: "Outfit from existing items set successfully"
    });
  } catch (error) {
    console.error("[setExistingItemsOutfit] Error:", error);

    if (error instanceof ManyChatError) {
      return formatResponse(error.statusCode, {
        success: false,
        message: error.message,
        errorCode: error.errorCode
      });
    }

    if (error instanceof OutfitError) {
      return formatResponse(error.statusCode, {
        success: false,
        message: error.message,
        errorCode: error.errorCode || "OUTFIT_ERROR"
      });
    }

    return formatResponse(500, {
      success: false,
      message: error instanceof Error ? error.message : "Unknown error occurred",
      errorCode: "INTERNAL_SERVER_ERROR"
    });
  }
};
