import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { ManyChatError, formatResponse } from "../../utils/errors";
import { ManychatUtilService } from "../../../../services/manychat/manychat.util";
import { ManychatCommonService } from "../../../../services/manychat/manychat.common";
import { MANYCHAT_FIELD_IDS } from "../../../../external/manychat/constants";

export const setOutfitImageGenAI = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    console.log("[setOutfitImageGenAI] Controller started");

    if (!event.body) {
      throw new ManyChatError(
        "Request body is required",
        400,
        "MISSING_REQUEST_BODY"
      );
    }

    let requestBody: any;
    try {
      requestBody = JSON.parse(event.body);
    } catch (e) {
      throw new ManyChatError(
        "Invalid JSON in request body",
        400,
        "INVALID_JSON"
      );
    }

    // Extract required parameters
    const { outfitText, manychatSubscriberId, userId, operationId } = requestBody;

    // Initialize services
    const manychatUtilService = new ManychatUtilService();
    const manychatCommonService = new ManychatCommonService();

    // Validate required fields - for async operations, set error in Manychat instead of throwing
    // First check if we have manychatSubscriberId to set error field, then check other fields
    if (!manychatSubscriberId) {
      console.log("[setOutfitImageGenAI] Missing manychatSubscriberId - cannot set Manychat error field");
      return formatResponse(200, {
        success: false,
        message: "Missing required field: manychatSubscriberId"
      });
    }

    // Now check other required fields and set error in Manychat if any are missing
    if (!outfitText || !userId || !operationId) {
      console.log("[setOutfitImageGenAI] Missing required fields", {
        hasOutfitText: !!outfitText,
        hasUserId: !!userId,
        hasOperationId: !!operationId
      });

      // Set error status in Manychat for missing required fields
      try {
        await manychatCommonService.setCustomFields(Number(manychatSubscriberId), [
          {
            field_id: MANYCHAT_FIELD_IDS.OUTFIT_IMAGE_LINK,
            field_value: "ERROR"
          }
        ]);
        console.log("[setOutfitImageGenAI] Set error status in Manychat for missing required fields");
      } catch (manychatError) {
        console.error("[setOutfitImageGenAI] Failed to set error status in Manychat for missing fields:", manychatError);
      }

      return formatResponse(200, {
        success: false,
        message: "Missing required fields: outfitText, userId, or operationId"
      });
    }

    console.log("[setOutfitImageGenAI] Processing request", {
      userId,
      manychatSubscriberId,
      operationId,
      outfitTextLength: outfitText.length
    });

    try {
      // Generate outfit image using the util service
      const result = await manychatUtilService.generateOutfitImage({
        outfitText,
        userId,
        operationId
      });

      if (result.error) {
        // Image generation failed - set error status in Manychat
        console.log(`[setOutfitImageGenAI] Image generation failed for user ${userId}:`, result.message);
        
        await manychatCommonService.setCustomFields(Number(manychatSubscriberId), [
          {
            field_id: MANYCHAT_FIELD_IDS.OUTFIT_IMAGE_LINK,
            field_value: "ERROR"
          }
        ]);

        console.log(`[setOutfitImageGenAI] Set error status in Manychat for user ${userId}`);
      } else if (result.imageUrl) {
        // Image generation succeeded - set image URL in Manychat
        console.log(`[setOutfitImageGenAI] Image generation succeeded for user ${userId}`);
        
        await manychatCommonService.setCustomFields(Number(manychatSubscriberId), [
          {
            field_id: MANYCHAT_FIELD_IDS.OUTFIT_IMAGE_LINK,
            field_value: result.imageUrl
          }
        ]);

        console.log(`[setOutfitImageGenAI] Set image URL in Manychat for user ${userId}`);
      }

      // Return success response regardless of image generation result
      return formatResponse(200, {
        success: true,
        message: result.message,
        operationId
      });

    } catch (serviceError) {
      // Handle any service errors by setting error status in Manychat
      console.error(`[setOutfitImageGenAI] Service error for user ${userId}:`, serviceError);
      
      try {
        await manychatCommonService.setCustomFields(Number(manychatSubscriberId), [
          {
            field_id: MANYCHAT_FIELD_IDS.OUTFIT_IMAGE_LINK,
            field_value: "ERROR"
          }
        ]);
        console.log(`[setOutfitImageGenAI] Set error status in Manychat after service error for user ${userId}`);
      } catch (manychatError) {
        console.error(`[setOutfitImageGenAI] Failed to set error status in Manychat for user ${userId}:`, manychatError);
      }

      // Return success response with error message (following Manychat pattern)
      return formatResponse(200, {
        success: false,
        message: serviceError instanceof Error ? serviceError.message : "Unknown error occurred during image generation",
        operationId
      });
    }

  } catch (error) {
    console.error("[setOutfitImageGenAI] Controller error:", error);

    if (error instanceof ManyChatError) {
      return formatResponse(
        error.statusCode,
        { message: error.message },
        error.errorCode
      );
    }

    return formatResponse(
      500,
      { message: "Internal server error" },
      "INTERNAL_SERVER_ERROR"
    );
  }
};
