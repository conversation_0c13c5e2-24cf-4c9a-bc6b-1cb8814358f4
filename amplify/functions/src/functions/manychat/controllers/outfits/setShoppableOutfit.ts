import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { formatResponse, ManyChatError } from "../../utils/errors";
import { manychatOutfitService } from "../../../../services/manychat";
import { SetShoppableOutfitRequest } from "../../../../types/manychat";

export const setShoppableOutfit = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    const body = JSON.parse(event.body || "{}");

    // Validate required fields
    if (!body.userId || !body.outfit || !body.apparelProfile) {
      throw new ManyChatError(
        "Missing required fields: userId, outfit, and apparelProfile",
        400,
        "MISSING_REQUIRED_FIELDS"
      );
    }

    const request: SetShoppableOutfitRequest = {
      userId: body.userId,
      outfit: body.outfit,
      apparelProfile: body.apparelProfile
    };

    // Process synchronously and wait for completion
    await manychatOutfitService.setShoppableOutfit(request);

    // Return success response after processing is complete
    return formatResponse(200, {
      message: "Webhook processed successfully",
      status: "completed"
    });
  } catch (error) {
    console.error("Error processing ManyChat webhook:", error);
    if (error instanceof ManyChatError) {
      throw error;
    }
    throw new ManyChatError(
      "Failed to process ManyChat webhook",
      500,
      "WEBHOOK_PROCESSING_ERROR"
    );
  }
};