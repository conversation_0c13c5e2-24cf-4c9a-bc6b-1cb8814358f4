import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { ManyChatError, formatResponse } from "../../utils/errors";
import { ManychatUtilService } from "../../../../services/manychat/manychat.util";

const utilService = new ManychatUtilService();

/**
 * Controller for validating city from user input for Manychat
 * @param event APIGatewayProxyEvent
 * @returns APIGatewayProxyResult
 */
export const getValidCityFromText = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  console.log("[getValidCityFromText] Controller started");
  console.log("[getValidCityFromText] Raw event:", JSON.stringify(event));
  try {
    if (!event.body) {
      throw new ManyChatError(
        "Request body is required",
        200,
        "MISSING_REQUEST_BODY"
      );
    }
    let requestBody: any;
    try {
      requestBody = JSON.parse(event.body);
      console.log("[getValidCityFromText] Parsed request body:", requestBody);
    } catch (e) {
      throw new ManyChatError(
        "Invalid JSON in request body",
        200,
        "INVALID_JSON"
      );
    }
    const { manychatSubscriberId, userInput } = requestBody;
    if (!manychatSubscriberId || !userInput) {
      throw new ManyChatError(
        "Missing required fields: manychatSubscriberId and userInput are required",
        200,
        "MISSING_REQUIRED_FIELD"
      );
    }
    const result = await utilService.getValidCityFromText({ manychatSubscriberId, userInput });
    console.log("[getValidCityFromText] Result to return:", result);
    console.log("[getValidCityFromText] Controller end");
    return formatResponse(200, result);
  } catch (error) {
    console.error("[getValidCityFromText] Error:", {
      error: error instanceof Error ? error.message : 'Unknown error',
      errorType: error instanceof Error ? error.constructor.name : typeof error
    });
    if (error instanceof ManyChatError) {
      return formatResponse(200, {
        response: 'ERROR',
        message: error.message
      }, error.errorCode);
    }
    return formatResponse(200, {
      response: 'ERROR',
      message: 'Sorry, something went wrong while processing your request.'
    }, "INTERNAL_SERVER_ERROR");
  }
}; 