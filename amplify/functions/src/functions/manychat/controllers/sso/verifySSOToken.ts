import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { ManyChatError, formatResponse } from "../../utils/errors";
import { ManychatVerifySSOTokenRequest, ManychatVerifySSOTokenResponse } from "../../../../types/manychat";
import { SSOService } from "../../../../services/ssoService";
import { JWTService } from "../../../../services/jwtService";
import { ManychatCommonService } from "../../../../services/manychat/manychat.common";
import { MANYCHAT_FIELD_IDS } from "../../../../external/manychat/constants";

const ssoService = new SSOService();
const jwtService = new JWTService();
const manychatCommonService = new ManychatCommonService();

/**
 * Controller for verifying SSO token for Manychat users
 * @param event APIGatewayProxyEvent
 * @returns APIGatewayProxyResult
 */
export const verifySSOToken = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  console.log("[verifySSOToken] Controller started");
  console.log("[verifySSOToken] Raw event:", JSON.stringify(event));

  try {
    if (!event.body) {
      throw new ManyChatError(
        "Request body is required",
        200,
        "MISSING_REQUEST_BODY"
      );
    }

    let requestBody: any;
    try {
      requestBody = JSON.parse(event.body);
      console.log("[verifySSOToken] Parsed request body:", requestBody);
    } catch (e) {
      throw new ManyChatError(
        "Invalid JSON in request body",
        200,
        "INVALID_JSON"
      );
    }

    const { token, manychatSubscriberId } = requestBody as ManychatVerifySSOTokenRequest;

    // Validate required fields
    if (!token || !manychatSubscriberId) {
      throw new ManyChatError(
        "Missing required fields: token and manychatSubscriberId are required",
        200,
        "MISSING_REQUIRED_FIELD"
      );
    }

    console.log("[verifySSOToken] Processing SSO token verification for:", {
      token,
      manychatSubscriberId
    });

    // Validate the SSO token using the service
    const validationResult = await ssoService.validateSSOToken(token);

    if (!validationResult.valid) {
      console.log("[verifySSOToken] Token validation failed:", validationResult.reason);

      // Set Manychat field to indicate verification failure
      try {
        await manychatCommonService.setCustomFields(
          parseInt(manychatSubscriberId),
          [
            {
              field_id: MANYCHAT_FIELD_IDS.TOKEN_VERIFICATION_STATUS,
              field_value: false
            }
          ]
        );
      } catch (manychatError) {
        console.error("[verifySSOToken] Error setting Manychat field for failed verification:", manychatError);
        // Continue with the response even if Manychat field setting fails
      }

      // Prepare failure response
      const response: ManychatVerifySSOTokenResponse = {
        success: false,
        message: `Token verification failed: ${validationResult.reason}`,
        timestamp: new Date().toISOString()
      };

      console.log("[verifySSOToken] Token verification failed");
      return formatResponse(200, response);
    }

    // Token is valid, create JWT
    const userId = validationResult.userId!;
    const signingKey = `${token}SECRET`;

    try {
      const jwt = await jwtService.createJWT(userId, signingKey);

      // Set Manychat field to indicate verification success
      try {
        await manychatCommonService.setCustomFields(
          parseInt(manychatSubscriberId),
          [
            {
              field_id: MANYCHAT_FIELD_IDS.TOKEN_VERIFICATION_STATUS,
              field_value: true
            }
          ]
        );
      } catch (manychatError) {
        console.error("[verifySSOToken] Error setting Manychat field for successful verification:", manychatError);
        // Continue with the response even if Manychat field setting fails
      }

      // Prepare success response
      const response: ManychatVerifySSOTokenResponse = {
        jwt,
        success: true,
        message: "SSO token verified successfully",
        timestamp: new Date().toISOString()
      };

      console.log("[verifySSOToken] SSO token verified successfully:", {
        token,
        userId,
        jwtCreated: true
      });

      console.log("[verifySSOToken] Controller end");
      return formatResponse(200, response);

    } catch (jwtError) {
      console.error("[verifySSOToken] Error creating JWT:", jwtError);

      // Set Manychat field to indicate verification failure
      try {
        await manychatCommonService.setCustomFields(
          parseInt(manychatSubscriberId),
          [
            {
              field_id: MANYCHAT_FIELD_IDS.TOKEN_VERIFICATION_STATUS,
              field_value: false
            }
          ]
        );
      } catch (manychatError) {
        console.error("[verifySSOToken] Error setting Manychat field for JWT creation failure:", manychatError);
      }

      throw new ManyChatError(
        "Failed to create JWT token",
        200,
        "JWT_CREATION_FAILED"
      );
    }

  } catch (error) {
    console.error("[verifySSOToken] Error:", {
      error: error instanceof Error ? error.message : 'Unknown error',
      errorType: error instanceof Error ? error.constructor.name : typeof error
    });

    // Handle specific errors
    if (error instanceof ManyChatError) {
      return formatResponse(
        error.statusCode,
        {
          success: false,
          message: error.message,
          timestamp: new Date().toISOString()
        },
        error.errorCode
      );
    }

    // Handle unexpected errors
    return formatResponse(
      200, // Always return 200 for Manychat compatibility
      {
        success: false,
        message: "Failed to verify SSO token",
        timestamp: new Date().toISOString(),
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      },
      "SSO_TOKEN_VERIFICATION_FAILED"
    );
  }
};
