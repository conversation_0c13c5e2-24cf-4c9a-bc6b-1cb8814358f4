import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { ManyChatError, formatResponse } from "../../utils/errors";
import { ManychatGetSSOLinkRequest, ManychatGetSSOLinkResponse } from "../../../../types/manychat";
import { SSOService } from "../../../../services/ssoService";

const ssoService = new SSOService();

/**
 * Controller for generating SSO link for Manychat users
 * @param event APIGatewayProxyEvent
 * @returns APIGatewayProxyResult
 */
export const getSSOLink = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  console.log("[getSSOLink] Controller started");
  console.log("[getSSOLink] Raw event:", JSON.stringify(event));

  try {
    if (!event.body) {
      throw new ManyChatError(
        "Request body is required",
        200,
        "MISSING_REQUEST_BODY"
      );
    }

    let requestBody: any;
    try {
      requestBody = JSON.parse(event.body);
      console.log("[getSSOLink] Parsed request body:", requestBody);
    } catch (e) {
      throw new ManyChatError(
        "Invalid JSON in request body",
        200,
        "INVALID_JSON"
      );
    }

    const { manychatSubscriberId, userId } = requestBody as ManychatGetSSOLinkRequest;

    // Validate required fields
    if (!manychatSubscriberId || !userId) {
      throw new ManyChatError(
        "Missing required fields: manychatSubscriberId and userId are required",
        200,
        "MISSING_REQUIRED_FIELD"
      );
    }

    console.log("[getSSOLink] Processing SSO link generation for:", {
      manychatSubscriberId,
      userId
    });

    // Generate SSO link using service
    const { ssoLink, token } = await ssoService.generateSSOLink(userId, manychatSubscriberId);

    // Prepare response
    const response: ManychatGetSSOLinkResponse = {
      ssoLink,
      success: true,
      message: "SSO link generated successfully",
      timestamp: new Date().toISOString()
    };

    console.log("[getSSOLink] SSO link generated successfully:", {
      token,
      userId,
      ssoLink
    });

    console.log("[getSSOLink] Controller end");
    return formatResponse(200, response);

  } catch (error) {
    console.error("[getSSOLink] Error:", {
      error: error instanceof Error ? error.message : 'Unknown error',
      errorType: error instanceof Error ? error.constructor.name : typeof error
    });

    // Handle specific errors
    if (error instanceof ManyChatError) {
      return formatResponse(
        error.statusCode,
        {
          success: false,
          message: error.message,
          timestamp: new Date().toISOString()
        },
        error.errorCode
      );
    }

    // Handle unexpected errors
    return formatResponse(
      200, // Always return 200 for Manychat compatibility
      {
        success: false,
        message: "Failed to generate SSO link",
        timestamp: new Date().toISOString(),
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      },
      "SSO_LINK_GENERATION_FAILED"
    );
  }
}; 