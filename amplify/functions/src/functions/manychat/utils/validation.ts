/**
 * Utility functions for validating Manychat data
 */

import { MANYCHAT_PLACEHOLDERS } from "../../../external/manychat/constants";


/**
 * Checks if a value from Manychat is valid or a placeholder
 * @param value The value to check
 * @returns true if the value is valid, false if it's a placeholder like "NA" or starts with "{{"
 */
export const isValidManychatValue = (value?: string): boolean => {
  if (!value) return false;

  // Check for common placeholder values from Manychat
  if (value === MANYCHAT_PLACEHOLDERS.NA || value.startsWith(MANYCHAT_PLACEHOLDERS.DOUBLE_BRACES_PREFIX)) {
    return false;
  }

  return true;
};
