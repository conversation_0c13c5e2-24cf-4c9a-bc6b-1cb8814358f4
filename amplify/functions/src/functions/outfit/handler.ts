import { APIGatewayProxyHandler } from "aws-lambda";
import { handleRoutes } from "./routes";

export const handler: APIGatewayProxyHandler = async (event, context) => {
  console.log("Event received:", {
    path: event.path,
    method: event.httpMethod,
    body: event.body,
    requestId: context.awsRequestId,
    RTCCertificate: event.headers?.RTCCertificate,
  });

  // Handle CORS preflight requests
  if (event.httpMethod === "OPTIONS") {
    console.log("[<PERSON><PERSON>] Handling CORS preflight request");
    return {
      statusCode: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "OPTIONS,POST,GET,PUT,DELETE",
        "Access-Control-Allow-Headers":
          "Content-Type,Authorization,X-Amz-Date,X-Api-Key",
      },
      body: JSON.stringify({
        message: "CORS preflight successful",
        timestamp: new Date().toISOString(),
      }),
    };
  }

  return await handleRoutes(event, context);
};