import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { OutfitCollectionService } from "../../../../services/outfitCollectionService";
import { OutfitError } from "../../utils/errors";

export const getCollectionController = async (
  event: APIGatewayProxyEvent,
  collectionId: string
): Promise<APIGatewayProxyResult> => {
  if (!collectionId) {
    throw new OutfitError(
      "Missing required parameter: collectionId",
      400,
      "MISSING_COLLECTION_ID"
    );
  }

  const queryParams = event.queryStringParameters || {};
  const { userId } = queryParams;

  if (!userId) {
    throw new OutfitError(
      "Missing required parameter: userId",
      400,
      "MISSING_USER_ID"
    );
  }

  const collectionService = new OutfitCollectionService();
  const collection = await collectionService.getOutfitCollection(userId, collectionId);

  if (!collection) {
    throw new OutfitError(
      "Collection not found",
      404,
      "COLLECTION_NOT_FOUND"
    );
  }

  return {
    statusCode: 200,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Credentials": true,
    },
    body: JSON.stringify({
      data: collection,
      timestamp: new Date().toISOString(),
    }),
  };
};