import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { CreateOutfitCollectionInput } from "../../../../types/outfits.types";
import { OutfitError } from "../../utils/errors";
import { OutfitCollectionService } from "../../../../services/outfitCollectionService";

export const createCollectionController = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  if (!event.body) {
    throw new OutfitError(
      "Request body is required",
      400,
      "INVALID_REQUEST"
    );
  }

  const queryParams = event.queryStringParameters || {};
  const { userId } = queryParams;

  if (!userId) {
    throw new OutfitError(
      "Missing required parameter: userId",
      400,
      "MISSING_USER_ID"
    );
  }

  const createRequest: Omit<CreateOutfitCollectionInput, "userId"> = JSON.parse(
    event.body
  );

  if (!createRequest.outfitCollectionName) {
    throw new OutfitError(
      "Missing required field: outfitCollectionName",
      400,
      "MISSING_FIELDS"
    );
  }

  const collectionService = new OutfitCollectionService();
  const collection = await collectionService.createOutfitCollection({
    ...createRequest,
    userId,
    outfitPointers: createRequest.outfitPointers || [], // Initialize with empty array if not provided
  });

  return {
    statusCode: 201,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Credentials": true,
    },
    body: JSON.stringify({
      data: collection,
      timestamp: new Date().toISOString(),
    }),
  };
}; 