import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { OutfitCollectionService } from "../../../../services/outfitCollectionService";
import { OutfitError } from "../../utils/errors";

export const listCollectionsController = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  const queryParams = event.queryStringParameters || {};
  const { userId } = queryParams;

  if (!userId) {
    throw new OutfitError(
      "Missing required parameter: userId",
      400,
      "MISSING_USER_ID"
    );
  }

  const collectionService = new OutfitCollectionService();
  const collections = await collectionService.listOutfitCollections(userId);

  return {
    statusCode: 200,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "OPTIONS,POST,GET,PUT,DELETE",
      "Access-Control-Allow-Headers":
        "Content-Type,Authorization,X-Amz-Date,X-Api-Key",
      "Access-Control-Allow-Credentials": "true",
    },
    body: JSON.stringify({
      data: collections,
      timestamp: new Date().toISOString(),
    }),
  };
};