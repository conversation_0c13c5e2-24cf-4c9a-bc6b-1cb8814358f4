import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { OutfitCollectionService } from "../../../../services/outfitCollectionService";
import { UpdateOutfitCollectionInput } from "../../../../types/outfits.types";
import { OutfitError } from "../../utils/errors";

export const updateCollectionController = async (
  event: APIGatewayProxyEvent,
  collectionId: string
): Promise<APIGatewayProxyResult> => {
  if (!collectionId) {
    throw new OutfitError(
      "Missing required parameter: collectionId",
      400,
      "MISSING_COLLECTION_ID"
    );
  }

  if (!event.body) {
    throw new OutfitError(
      "Request body is required",
      400,
      "INVALID_REQUEST"
    );
  }

  const queryParams = event.queryStringParameters || {};
  const { userId } = queryParams;

  if (!userId) {
    throw new OutfitError(
      "Missing required parameter: userId",
      400,
      "MISSING_USER_ID"
    );
  }

  const updates: UpdateOutfitCollectionInput = JSON.parse(event.body);
  const collectionService = new OutfitCollectionService();
  const updatedCollection = await collectionService.updateOutfitCollection(
    userId,
    collectionId,
    updates
  );

  return {
    statusCode: 200,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Credentials": true,
    },
    body: JSON.stringify({
      data: updatedCollection,
      timestamp: new Date().toISOString(),
    }),
  };
}; 