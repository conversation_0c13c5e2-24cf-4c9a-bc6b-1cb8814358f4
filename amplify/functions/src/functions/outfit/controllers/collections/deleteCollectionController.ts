import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { OutfitCollectionService } from "../../../../services/outfitCollectionService";
import { OutfitError } from "../../utils/errors";

export const deleteCollectionController = async (
  event: APIGatewayProxyEvent,
  collectionId: string
): Promise<APIGatewayProxyResult> => {
  if (!collectionId) {
    throw new OutfitError(
      "Missing required parameter: collectionId",
      400,
      "MISSING_COLLECTION_ID"
    );
  }

  const queryParams = event.queryStringParameters || {};
  const { userId } = queryParams;

  if (!userId) {
    throw new OutfitError(
      "Missing required parameter: userId",
      400,
      "MISSING_USER_ID"
    );
  }

  const collectionService = new OutfitCollectionService();
  await collectionService.deleteOutfitCollection(userId, collectionId);

  return {
    statusCode: 200,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Credentials": true,
    },
    body: JSON.stringify({
      data: { message: "Collection deleted successfully" },
      timestamp: new Date().toISOString(),
    }),
  };
}; 