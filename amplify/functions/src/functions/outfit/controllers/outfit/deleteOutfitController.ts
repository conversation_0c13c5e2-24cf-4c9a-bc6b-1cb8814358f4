import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { OutfitService } from "../../../../services/outfitService";
import { OutfitError } from "../../utils/errors";

export const deleteOutfitController = async (
  event: APIGatewayProxyEvent,
  outfitId: string
): Promise<APIGatewayProxyResult> => {
  if (!event.body) {
    throw new OutfitError(
      "Request body is required",
      400,
      "INVALID_REQUEST"
    );
  }

  const queryParams = event.queryStringParameters || {};
  const { userId } = queryParams;

  if (!userId) {
    throw new OutfitError(
      "Missing required parameter: userId",
      400,
      "MISSING_USER_ID"
    );
  }

  if (!outfitId) {
    throw new OutfitError(
      "Missing required parameter: outfitId",
      400,
      "MISSING_OUTFIT_ID"
    );
  }

  const outfitCollectionId = JSON.parse(event.body);
  if (!outfitCollectionId) {
    throw new OutfitError(
      "Missing required parameter: outfitCollectionId",
      400,
      "MISSING_COLLECTION_ID"
    );
  }

  const outfitService = new OutfitService();
  await outfitService.archiveOutfit(userId, outfitId, outfitCollectionId);

  return {
    statusCode: 200,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Credentials": true,
    },
    body: JSON.stringify({
      data: { message: "Outfit deleted successfully" },
      timestamp: new Date().toISOString(),
    }),
  };
}; 