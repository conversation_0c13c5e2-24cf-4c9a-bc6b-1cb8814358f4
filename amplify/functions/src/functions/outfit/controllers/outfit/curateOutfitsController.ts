import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { OutfitService } from "../../../../services/outfitService";
import { OutfitError } from "../../utils/errors";

export const curateOutfitsController = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    const queryParams = event.queryStringParameters || {};
    const { userId, time, location, weather } = queryParams;

    if (!userId) {
      throw new OutfitError(
        "Missing required parameter: userId",
        400,
        "MISSING_USER_ID"
      );
    }

    const outfitService = new OutfitService();
    const curatedOutfits = await outfitService.curateOutfits({
      userId,
      time,
      location,
      weather,
    });

    return {
      statusCode: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "OPTIONS,POST,GET,PUT,DELETE",
        "Access-Control-Allow-Headers":
          "Content-Type,Authorization,X-Amz-Date,X-Api-Key",
        "Access-Control-Allow-Credentials": "true",
      },
      body: JSON.stringify({
        data: curatedOutfits,
        timestamp: new Date().toISOString(),
      }),
    };
  } catch (error) {
    console.error("Error in curateOutfitsController:", error);

    if (error instanceof OutfitError) {
      return {
        statusCode: error.statusCode,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "OPTIONS,POST,GET,PUT,DELETE",
          "Access-Control-Allow-Headers":
            "Content-Type,Authorization,X-Amz-Date,X-Api-Key",
          "Access-Control-Allow-Credentials": "true",
        },
        body: JSON.stringify({
          data: { message: error.message },
          errorCode: error.errorCode,
          timestamp: new Date().toISOString(),
        }),
      };
    }

    return {
      statusCode: 500,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "OPTIONS,POST,GET,PUT,DELETE",
        "Access-Control-Allow-Headers":
          "Content-Type,Authorization,X-Amz-Date,X-Api-Key",
        "Access-Control-Allow-Credentials": "true",
      },
      body: JSON.stringify({
        data: { message: "An unexpected error occurred" },
        errorCode: "INTERNAL_ERROR",
        timestamp: new Date().toISOString(),
      }),
    };
  }
};