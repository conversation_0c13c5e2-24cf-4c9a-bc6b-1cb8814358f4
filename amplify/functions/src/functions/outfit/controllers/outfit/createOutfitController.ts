import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { OutfitService } from "../../../../services/outfitService";
import { CreateOutfitInput } from "../../../../types/outfits.types";
import { OutfitError } from "../../utils/errors";

export const createOutfitController = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  if (!event.body) {
    throw new OutfitError(
      "Request body is required",
      400,
      "INVALID_REQUEST"
    );
  }

  const queryParams = event.queryStringParameters || {};
  const { userId } = queryParams;

  if (!userId) {
    throw new OutfitError(
      "Missing required parameter: userId",
      400,
      "MISSING_USER_ID"
    );
  }

  const createRequest: Omit<CreateOutfitInput, "userId"> = JSON.parse(
    event.body
  );

  if (!createRequest.outfitName || !createRequest.apparelPointers || !createRequest.situationContext) {
    throw new OutfitError(
      "Missing required fields: outfitName, apparelPointers, and situationContext are required",
      400,
      "MISSING_FIELDS"
    );
  }

  const outfitService = new OutfitService();
  const outfit = await outfitService.createOutfit({
    ...createRequest,
    userId,
  });

  return {
    statusCode: 201,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Credentials": true,
    },
    body: JSON.stringify({
      data: outfit,
      timestamp: new Date().toISOString(),
    }),
  };
};