import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { OutfitService } from "../../../../services/outfitService";
import { UpdateOutfitInput } from "../../../../types/outfits.types";
import { OutfitError } from "../../utils/errors";

export const updateOutfitController = async (
  event: APIGatewayProxyEvent,
  outfitId: string
): Promise<APIGatewayProxyResult> => {
  if (!outfitId) {
    throw new OutfitError(
      "Missing required parameter: outfitId",
      400,
      "MISSING_OUTFIT_ID"
    );
  }

  if (!event.body) {
    throw new OutfitError(
      "Request body is required",
      400,
      "INVALID_REQUEST"
    );
  }

  const queryParams = event.queryStringParameters || {};
  const { userId } = queryParams;

  if (!userId) {
    throw new OutfitError(
      "Missing required parameter: userId",
      400,
      "MISSING_USER_ID"
    );
  }

  const updates: UpdateOutfitInput = JSON.parse(event.body);
  const outfitService = new OutfitService();
  const updatedOutfit = await outfitService.updateOutfit(
    userId,
    outfitId,
    updates
  );

  return {
    statusCode: 200,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Credentials": true,
    },
    body: JSON.stringify({
      data: updatedOutfit,
      timestamp: new Date().toISOString(),
    }),
  };
}; 