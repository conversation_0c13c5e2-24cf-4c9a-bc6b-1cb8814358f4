import { APIGatewayProxyEvent } from "aws-lambda";
import { OutfitError } from "../utils/errors";
import { AxiosClient } from "../../../config/axiosClient";
import AUTH_ENDPOINT from "../../../config/constants";

const authClient = AxiosClient.getInstance(AUTH_ENDPOINT).getClient();

const verifyAuthToken = async (token: string): Promise<boolean> => {
  console.log("[Handler][VerifyAuthToken] Verifying auth token");
  try {
    const response = await authClient.post(
      "/auth/token/verify",
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.status === 200;
  } catch (error) {
    console.error(
      "[Hand<PERSON>][VerifyAuthToken] Token verification failed:",
      error
    );
    return false;
  }
};

export const authenticate = async (event: APIGatewayProxyEvent): Promise<void> => {
  const authHeader = event.headers.Authorization || event.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    throw new OutfitError("Unauthorized", 401, "UNAUTHORIZED");
  }

  const token = authHeader.replace("Bearer ", "");
  const isValid = await verifyAuthToken(token);
  if (!isValid) {
    throw new OutfitError("Invalid or expired token", 401, "UNAUTHORIZED");
  }
}; 