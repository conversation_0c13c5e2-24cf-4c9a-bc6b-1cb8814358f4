import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { createOutfitController } from "./controllers/outfit/createOutfitController";
import { updateOutfitController } from "./controllers/outfit/updateOutfitController";
import { deleteOutfitController } from "./controllers/outfit/deleteOutfitController";
import { createCollectionController } from "./controllers/collections/createCollectionController";
import { updateCollectionController } from "./controllers/collections/updateCollectionController";
import { deleteCollectionController } from "./controllers/collections/deleteCollectionController";
import { getCollectionController } from "./controllers/collections/getCollectionController";
import { listCollectionsController } from "./controllers/collections/listCollectionsController";
import { getOptimizedOutfitController } from "./controllers/outfit/getOptimizedOutfitController";
import { curateOutfitsController } from "./controllers/outfit/curateOutfitsController";
import { getWeeklyOutfitsController } from "./controllers/collections/getWeeklyOutfitsController";
import { getTodayOutfitsController } from "./controllers/collections/getTodayOutfitsController";
import { getDayVibeOutfitController } from "./controllers/collections/getDayVibeOutfitController";
import { authenticate } from "./middleware/auth";
import { OutfitError } from "./utils/errors";

export const handleRoutes = async (
  event: APIGatewayProxyEvent,
  context: any
): Promise<APIGatewayProxyResult> => {
  try {
    // Extract common params and validate path
    const pathParts = event.path.split("/");
    const resourceType = pathParts[pathParts.length - 2]; // "outfits" or "collections"
    const resourceId = pathParts[pathParts.length - 1];

    // Authenticate the request
    //await authenticate(event);

    // Route based on resource type and method
    if (resourceType === "outfits" || resourceType === undefined) {
      // Handle outfit routes
      switch (event.httpMethod) {
        case "POST":
          return await createOutfitController(event);
        case "PUT":
          return await updateOutfitController(event, resourceId);
        case "DELETE":
          return await deleteOutfitController(event, resourceId);
        case "GET":
          if (resourceId === "get-outfit") {
            return await getOptimizedOutfitController(event);
          }
          if (resourceId === "get-outfits") {
            return await curateOutfitsController(event);
          }
          throw new OutfitError(
            `Invalid outfit resource ID: ${resourceId}`,
            400,
            "INVALID_RESOURCE"
          );
        default:
          throw new OutfitError(
            `Method ${event.httpMethod} not supported for outfits`,
            405,
            "METHOD_NOT_ALLOWED"
          );
      }
    } else if (resourceType === "collections") {
      // Handle collection routes
      switch (event.httpMethod) {
        case "POST":
          return await createCollectionController(event);
        case "PUT":
          return await updateCollectionController(event, resourceId);
        case "DELETE":
          return await deleteCollectionController(event, resourceId);
        case "GET":
          if (resourceId === "list") {
            return await listCollectionsController(event);
          }
          if (resourceId === "get-weekly-outfits") {
            return await getWeeklyOutfitsController(event);
          }
          if (resourceId === "get-today-outfits") {
            return await getTodayOutfitsController(event);
          }
          if (resourceId === "get-day-vibe-outfit") {
            return await getDayVibeOutfitController(event);
          }
          return await getCollectionController(event, resourceId);
        default:
          throw new OutfitError(
            `Method ${event.httpMethod} not supported for collections`,
            405,
            "METHOD_NOT_ALLOWED"
          );
      }
    } else {
      throw new OutfitError(
        `Invalid resource type: ${resourceType}`,
        400,
        "INVALID_RESOURCE"
      );
    }
  } catch (error) {
    console.error("Error processing request:", {
      error,
      requestId: context.awsRequestId,
      path: event.path,
      method: event.httpMethod,
    });

    if (error instanceof OutfitError) {
      return {
        statusCode: error.statusCode,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Credentials": true,
        },
        body: JSON.stringify({
          data: { message: error.message },
          errorCode: error.errorCode,
          timestamp: new Date().toISOString(),
        }),
      };
    }

    if (error instanceof SyntaxError) {
      return {
        statusCode: 400,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Credentials": true,
        },
        body: JSON.stringify({
          data: { message: "Invalid JSON in request body" },
          errorCode: "INVALID_JSON",
          timestamp: new Date().toISOString(),
        }),
      };
    }

    return {
      statusCode: 500,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Credentials": true,
      },
      body: JSON.stringify({
        data: { message: "An unexpected error occurred" },
        errorCode: "INTERNAL_ERROR",
        timestamp: new Date().toISOString(),
      }),
    };
  }
}; 