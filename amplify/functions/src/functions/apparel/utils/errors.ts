export class ApparelError extends Error {
  constructor(
    message: string,
    public readonly statusCode: number = 500,
    public readonly errorCode?: string
  ) {
    super(message);
    this.name = "ApparelError";
  }
}

export const formatResponse = (
  statusCode: number,
  body: any,
  errorCode?: string
) => {
  return {
    statusCode,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*", // Allow all origins
      "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent",
      "Access-Control-Allow-Methods": "OPTIONS,POST,GET,PUT,DELETE,PATCH",
      "Access-Control-Allow-Credentials": "true" // Add this for credentials support
    },
    body: JSON.stringify({
      ...body,
      ...(errorCode && { errorCode }),
    }),
  };
}; 
