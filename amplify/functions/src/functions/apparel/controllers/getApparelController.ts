import { APIGatewayProxyEvent } from "aws-lambda";
import { ApparelService } from "../../../services/apparelService";
import { ApparelError } from "../utils/errors";

export const getApparelController = async (
  event: APIGatewayProxyEvent,
  apparelId: string
): Promise<{ statusCode: number, body: any }> => {

  if (!apparelId) {
    throw new ApparelError(
      "Missing apparelId in path",
      400,
      "MISSING_PARAMS"
    );
  }

  const queryParams = event.queryStringParameters || {};
  const userId = queryParams.userId;

  if (!userId) {
    throw new ApparelError(
      "Missing required query parameter: userId",
      400,
      "MISSING_PARAMS"
    );
  }

  const apparelService = new ApparelService();
  const apparel = await apparelService.getApparel(userId, apparelId);

  if (!apparel) {
    throw new ApparelError(
      `Apparel not found with ID: ${apparelId}`,
      404,
      "NOT_FOUND"
    );
  }

  return {
    statusCode: 200,
    body: apparel,
  };
};