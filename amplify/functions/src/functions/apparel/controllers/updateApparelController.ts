import { APIGatewayProxyEvent } from "aws-lambda";
import { ApparelService } from "../../../services/apparelService";
import { ApparelError } from "../utils/errors";
import { UpdateWardrobeInput } from "../../../types/apparel.types";

export const updateApparelController = async (
  event: APIGatewayProxyEvent,
  apparelId: string
): Promise<{ statusCode: number, body: any }> => {
  if (!event.body) {
    throw new ApparelError(
      "Request body is required",
      400,
      "INVALID_REQUEST"
    );
  }

  const queryParams = event.queryStringParameters || {};
  const userId = queryParams.userId;

  if (!userId) {
    throw new ApparelError(
      "Missing required query parameter: userId",
      400,
      "MISSING_PARAMS"
    );
  }

  const apparelService = new ApparelService();
  const updateRequest = JSON.parse(event.body);

  // If the request contains wardrobe-specific updates
  if (updateRequest.wardrobeApparelStatus || updateRequest.outfitCombinations) {
    const wardrobeUpdates: UpdateWardrobeInput = {
      wardrobeApparelStatus: updateRequest.wardrobeApparelStatus,
      outfitCombinations: updateRequest.outfitCombinations,
      apparel: updateRequest.apparel
    };

    await apparelService.updateWardrobeApparel(
      userId,
      apparelId,
      wardrobeUpdates
    );

    return {
      statusCode: 200,
      body: {
        message: "Wardrobe apparel updated successfully",
      },
    };
  }

  // Handle regular apparel update
  if (!updateRequest.source) {
    throw new ApparelError(
      "Missing required field: source is required for apparel updates",
      400,
      "MISSING_FIELDS"
    );
  }

  const updatedApparel = await apparelService.updateApparel(
    updateRequest.source,
    apparelId,
    userId,
    updateRequest.apparelMetaData
  );

  return {
    statusCode: 200,
    body: updatedApparel,
  };
};