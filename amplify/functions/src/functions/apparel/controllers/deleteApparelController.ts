import { APIGatewayProxyEvent } from "aws-lambda";
import { ApparelError } from "../utils/errors";
import { ApparelService } from "../../../services/apparelService";

export const deleteApparelController = async (
  event: APIGatewayProxyEvent,
  apparelId: string
): Promise<{ statusCode: number, body: any }> => {
  if (!apparelId) {
    throw new ApparelError(
      "Missing apparelId in path",
      400,
      "MISSING_PARAMS"
    );
  }

  const queryParams = event.queryStringParameters || {};
  const userId = queryParams.userId;

  if (!userId) {
    throw new ApparelError(
      "Missing required query parameter: userId",
      400,
      "MISSING_PARAMS"
    );
  }

  const apparelService = new ApparelService();

  await apparelService.archiveWardrobeApparel(userId, apparelId);

  return {
    statusCode: 200,
    body: {
      message: "Apparel archived successfully",
    },
  };
};