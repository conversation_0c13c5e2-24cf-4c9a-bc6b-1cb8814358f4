import { APIGatewayProxyEvent } from "aws-lambda";
import { ApparelService } from "../../../services/apparelService";
import { ApparelError } from "../utils/errors";
import { ApparelSource, ListApparelsRequest } from "../../../types/apparel.types";

export const listApparelsController = async (
  event: APIGatewayProxyEvent
): Promise<{ statusCode: number, body: any }> => {
  const queryParams = event.queryStringParameters || {};
  const listRequest: ListApparelsRequest = {
    userId: queryParams.userId as string,
  };

  if (!listRequest.userId) {
    throw new ApparelError(
      "Missing required query parameter: userId is required",
      400,
      "MISSING_PARAMS"
    );
  }

  const apparelService = new ApparelService();

  const apparels = await apparelService.listApparels(listRequest.userId);

  return {
    statusCode: 200,
    body: apparels,
  };
};