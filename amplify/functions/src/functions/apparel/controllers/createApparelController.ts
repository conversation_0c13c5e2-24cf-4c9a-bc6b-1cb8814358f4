import { APIGatewayProxyEvent } from "aws-lambda";
import { ApparelError } from "../utils/errors";
import { ApparelSource } from "../../../types/apparel.types";
import { ApparelService } from "../../../services/apparelService";

interface CreateApparelRequest {
  source: ApparelSource;
  userId: string;
  sourceId?: string;
  apparelMetaData: any; // Using any to handle both male and female metadata
}

export const createApparelController = async (
  event: APIGatewayProxyEvent
): Promise<{ statusCode: number, body: any }> => {
  if (!event.body) {
    throw new ApparelError(
      "Request body is required",
      400,
      "INVALID_REQUEST"
    );
  }

  const createRequest: CreateApparelRequest = JSON.parse(event.body);

  // Validate required fields
  if (
    !createRequest.apparelMetaData ||
    !createRequest.source ||
    !createRequest.sourceId
  ) {
    throw new ApparelError(
      "Missing required fields: apparelMetaData, source, and sourceId are required",
      400,
      "MISSING_FIELDS"
    );
  }

  const apparelService = new ApparelService();

  // Use the abstracted method that handles profile detection
  const apparel = await apparelService.createApparel(
    createRequest.source,
    createRequest.apparelMetaData,
    createRequest.userId,
    createRequest.sourceId
  );

  return {
    statusCode: 201,
    body: apparel,
  };
}; 
