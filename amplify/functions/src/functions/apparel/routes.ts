import { APIGatewayProxyEvent } from "aws-lambda";
import { ApparelError } from "./utils/errors";
import { createApparelController } from "./controllers/createApparelController";
import { updateApparelController } from "./controllers/updateApparelController";
import { getApparelController } from "./controllers/getApparelController";
import { listApparelsController } from "./controllers/listApparelsController";
import { deleteApparelController } from "./controllers/deleteApparelController";

export const handleRoutes = async (
  event: APIGatewayProxyEvent,
  context: any
): Promise<{ statusCode: number, body: any }> => {
  // Extract common params and validate path
  const pathParts = event.path.split("/");
  const apparelId = pathParts[pathParts.length - 1];

  switch (event.httpMethod) {
    case "POST":
      return await createApparelController(event);

    case "PUT":
      return await update<PERSON>ppa<PERSON><PERSON>ontroller(event, apparelId);

    case "GET":
      if (apparelId !== "list") {
        return await getApparelController(event, apparelId);
      } else {
        return await listApparelsController(event);
      }

    case "DELETE":
      return await deleteApparelController(event, apparelId);

    default:
      throw new ApparelError(
        `Method ${event.httpMethod} not supported`,
        405,
        "METHOD_NOT_ALLOWED"
      );
  }
};
