import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { FeedbackError } from "./utils/errors";
import { createFeedbackController } from "./controllers/createFeedbackController";
import { getFeedbackController } from "./controllers/getFeedbackController";
import { listFeedbackController } from "./controllers/listFeedbackController";

export const handleRoutes = async (
  event: APIGatewayProxyEvent,
  userId: string
): Promise<APIGatewayProxyResult> => {
  const pathParts = event.path.split("/");
  const lastPathPart = pathParts[pathParts.length - 1];

  switch (event.httpMethod) {
    case "POST":
      return await createFeedbackController(event);

    case "GET":
      if (lastPathPart !== "list" && lastPathPart !== "feedback") {
        // Assume the last part is feedbackId
        return await getFeedbackController(lastPathPart, userId);
      } else if (lastPathPart === "list") {
        return await listFeedbackController(userId);
      } else {
        throw new FeedbackError(
          "Invalid path for GET request",
          400,
          "INVALID_PATH"
        );
      }

    default:
      throw new FeedbackError(
        `Method ${event.httpMethod} not supported`,
        405,
        "METHOD_NOT_ALLOWED"
      );
  }
}; 