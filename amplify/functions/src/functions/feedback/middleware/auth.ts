import { APIGatewayProxyEvent } from "aws-lambda";
import { FeedbackError } from "../utils/errors";
import AUTH_ENDPOINT from "../../../config/constants";
import { AxiosClient } from "../../../config/axiosClient";

const authClient = AxiosClient.getInstance(AUTH_ENDPOINT).getClient();

const verifyAuthToken = async (token: string): Promise<{ userId: string }> => {
  console.log("[Handler][VerifyAuthToken] Verifying auth token");
  try {
    const response = await authClient.post(
      "/auth/token/verify",
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (response.status !== 200) {
      throw new FeedbackError("Invalid token", 401, "UNAUTHORIZED");
    }
    console.log(
      "[Handler][VerifyAuthToken] Token verified successfully",
      response.data
    );

    return { userId: response.data.data.user.userId };
  } catch (error) {
    console.error(
      "[<PERSON><PERSON>][VerifyAuthToken] Token verification failed:",
      error
    );
    throw new FeedbackError("Invalid or expired token", 401, "UNAUTHORIZED");
  }
};

export const authenticate = async (event: APIGatewayProxyEvent): Promise<string> => {
  const authHeader = event.headers.Authorization || event.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    throw new FeedbackError("Unauthorized", 401, "UNAUTHORIZED");
  }

  const token = authHeader.replace("Bearer ", "");
  const { userId } = await verifyAuthToken(token);

  if (!userId) {
    throw new FeedbackError("User ID not found in token", 401, "UNAUTHORIZED");
  }

  return userId;
}; 