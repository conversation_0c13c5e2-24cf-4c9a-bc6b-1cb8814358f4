import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { FeedbackError, formatResponse } from "../utils/errors";
import { FeedbackService } from "../../../services/feedbackService";

export const getFeedbackController = async (
  feedbackId: string,
  userId: string
): Promise<APIGatewayProxyResult> => {
  const feedbackService = new FeedbackService();
  const feedback = await feedbackService.getFeedbackById(userId, feedbackId);

  if (!feedback) {
    throw new FeedbackError(
      `Feedback not found with ID: ${feedbackId}`,
      404,
      "NOT_FOUND"
    );
  }

  return formatResponse(200, feedback);
}; 