import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { FeedbackError, formatResponse } from "../utils/errors";
import { FeedbackService } from "../../../services/feedbackService";

export const listFeedbackController = async (
  userId: string
): Promise<APIGatewayProxyResult> => {
  const feedbackService = new FeedbackService();
  const feedbackItems = await feedbackService.getFeedbackByUserId(userId);
  return formatResponse(200, feedbackItems);
}; 