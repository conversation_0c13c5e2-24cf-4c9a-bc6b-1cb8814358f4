import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { FeedbackError, formatResponse } from "../utils/errors";
import { CreateFeedbackRequest } from "../../../types/feedback.types";
import { FeedbackService } from "../../../services/feedbackService";

export const createFeedbackController = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  if (!event.body) {
    throw new FeedbackError("Request body is required", 400, "INVALID_REQUEST");
  }

  const createRequest: CreateFeedbackRequest = JSON.parse(event.body);

  if (!createRequest.feedbackText) {
    throw new FeedbackError(
      "Missing required field: feedbackText",
      400,
      "MISSING_FIELDS"
    );
  }

  const feedbackService = new FeedbackService();
  const feedback = await feedbackService.createFeedback(
    event.requestContext.authorizer?.userId,
    createRequest
  );

  return formatResponse(201, feedback);
}; 