export class FeedbackError extends Error {
  constructor(
    message: string,
    public readonly statusCode: number = 500,
    public readonly errorCode?: string
  ) {
    super(message);
    this.name = "FeedbackError";
  }
}

export const formatResponse = (
  statusCode: number,
  body: any,
  errorCode?: string
) => {
  return {
    statusCode,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "OPTIONS,POST,GET,PUT,DELETE",
      "Access-Control-Allow-Headers":
        "Content-Type,Authorization,X-Amz-Date,X-Api-Key",
      "Access-Control-Allow-Credentials": "true",
    },
    body: JSON.stringify({
      data: body,
      errorCode: errorCode,
      timestamp: new Date().toISOString(),
    }),
  };
};