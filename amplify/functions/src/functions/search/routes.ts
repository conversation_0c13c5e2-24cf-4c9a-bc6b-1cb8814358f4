import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { SearchError } from "./utils/errors";
import { searchByImageController } from "./controllers/searchByImageController";

export const handleRoutes = async (
  event: APIGatewayProxyEvent,
  context: any
): Promise<APIGatewayProxyResult> => {
  switch (event.httpMethod) {
    case "POST":
      return await searchByImageController(event);

    default:
      throw new SearchError(
        `Method ${event.httpMethod} not supported`,
        405,
        "METHOD_NOT_ALLOWED"
      );
  }
}; 