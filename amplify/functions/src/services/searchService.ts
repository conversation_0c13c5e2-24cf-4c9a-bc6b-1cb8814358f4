import { ApparelCollectionService } from "./apparelCollectionService";
import { ApparelProfile, ApparelMetaData } from "../types/apparel.types";
import { AIService } from "../ai/integrations/AIIntegration";
import { TypeChatService } from "../ai/integrations/TypeChatIntegration";
export class SearchService {
  private aiService: AIService;
  private typeChatService: TypeChatService;
  private apparelCollectionService: ApparelCollectionService;

  constructor() {
    console.log("[SearchService] Initializing service");
    this.aiService = new AIService();
    this.typeChatService = new TypeChatService();
    this.apparelCollectionService = new ApparelCollectionService();
  }

  private async validateImage(
    imageUrl: string
  ): Promise<{ valid: boolean; error?: string }> {
    try {
      console.log("[SearchService] Starting image validation:", { imageUrl });

      const systemPrompt = `Analyze this image and determine if it contains a single, clear piece of clothing or footwear worn by a human. 
      The image should not be obscene or unclear. Valid items include:
      - Clothing: shirts, pants, dresses, jackets, etc.
      - Footwear: shoes, boots, sandals, sneakers, etc.
      If the image contains multiple items or non-apparel items, mark it as invalid.
      Return only a JSON response in this format: {"valid": boolean, "error": string | null}`;

      console.log("[SearchService] Sending validation request to Gemini");
      const response = await this.aiService.getGeminiImageAnalysis(
        imageUrl,
        "Validate if this is a single piece of clothing or footwear",
        systemPrompt
      );

      console.log("[SearchService] Received validation response:", response);
      const parsedResponse = JSON.parse(response);
      console.log("[SearchService] Parsed validation result:", parsedResponse);

      return parsedResponse;
    } catch (error) {
      console.error("[SearchService] Error validating image:", {
        error,
        imageUrl,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      return { valid: false, error: "Failed to validate image" };
    }
  }

  private async extractAttributes(
    imageUrl: string,
    gender: ApparelProfile
  ): Promise<Partial<ApparelMetaData>> {
    try {
      console.log("[SearchService] Starting attribute extraction:", {
        imageUrl,
        gender,
      });

      const systemPrompt = `Analyze this clothing image and extract the following attributes based on the gender ${gender}.
      Focus on the most prominent piece of clothing in the image.
      
      First, determine the apparelCategory based on the type of clothing:
      - "TOPWEAR": shirts, t-shirts, tops, blouses, jackets, blazers, etc.
      - "BOTTOMWEAR": pants, jeans, trousers, shorts, skirts, etc.
      - "FOOTWEAR": shoes, boots, sandals, sneakers, etc.
      
      For ${gender === "MALE" ? "male" : "female"} clothing:
      ${
        gender === "MALE"
          ? `Required attributes (must be one of the following values):
         1. apparelCategory: Must be one of "TOPWEAR", "BOTTOMWEAR", or "FOOTWEAR"
         2. apparelProfile: Must be "MALE"
         3. apparelType: 
            - Clothing: tshirt, casual_shirt, formal_shirt, sweatshirt, sweater, jacket, blazer, suit, jeans, casual_trouser, formal_trouser, shorts, track_pants, kurta, waistcoat, rain_jacket, nehru_jackets
            - Footwear: casual_shoes, flats, sports_shoes, formal_shoes, boat_shoes, brogues, clogs, derbys, driving_shoes, espadrilles, flatforms, loafers, mojaris, monks, mule_sneakers, mules, oxfords, skate_shoes, slip_on_shoes, slip_on_sneakers, sneakers, trekking_shoes, biker_boots, chelsea_boots, chunky_boots, cowboy_boots, desert_boots, hiking_boots, monk_straps, rain_boots, regular_boots, winter_boots
         4. pattern: geometric, floral, solid, ethnic_motifs, striped, woven_design, polka_dots, chevron, abstract, checked, quirky, leheriya, paisley, embellished, colourblocked, bandhani, tribal, animal, tie_and_dye, graphic, conversational, brand_logo, typography, washed, camouflage, self_design, ombre, houndstooth, superhero, textured_self_design, solid_checked, solid_self_design
         5. fit: 
            - Clothing: slim_fit, regular_fit, relaxed_fit, straight_fit, loose_fit, tapered_fit, skinny_fit, bootcut, baggy, jogger, flared, oversized, tailored_fit, boxy, compression, muscle_fit
            - Footwear: slim_fit, regular_fit, tailored_fit
         6. fabric: 
            - Clothing: modal, cotton, blended, polyester, viscose_rayon, jacquard, linen, polycotton, cotton_linen, lyocell, organic_cotton, synthetic, corduroy, linen_blend, denim, satin, elastane, silk, cotton_silk, raw_silk, khadi, nylon, hemp, crepe, liva, acrylic, velvet, pure_cotton, wool, fleece, suede, leather, pu
            - Footwear: suede, pu, mesh, synthetic, textile, leather, canvas, synthetic_leather, synthetic_patent, synthetic_suede, velvet, nubuck, patent_leather, croslite, plastic, rubber, fabric, fur, faux_fur, lace
         7. color : blue, balck, grey, white and so on
         7. sleeves: long, short, sleeveless, three_quarter
         8. neckLineORCollar: spread_collar, button_down_collar, mandarin_collar, band_collar, hood, cuban_collar, slim_collar, cutaway_collar, club_collar, collarless, peter_pan_collar, wingtip_collar, built_up_collar
         9. shape: a_line, straight, pathani, anarkali, kaftan, pakistani_style
         10. transparency: opaque, sheer, semi_sheer
         11. length: regular, three_fourth_length, cropped
         12. waistRise: mid_rise, low_rise, high_rise
         13. fade: no_fade, light_fade, heavy_fade
         14. stretch: stretchable, non_stretchable
         15. ankleLength: regular, mid_top, high_top
         16. fastening: ankle_loop, backstrap, buckles, lace_ups, no_back_strap, slip_ons, velcro, zip
         17. toeType: open_toe, peep_toe, pointed_toe, round_toe, square_toe
         18. heelType: block, comfort, flatform, kitten, platform, slim, stilleto, wedge
         19. insole: eva_or_rubber, comfort_insole, padded, leather, memory_foam, arch_support, support_insole, poron, custom_orthotics, athletic, fur`
          : `Required attributes (must be one of the following values):
         1. apparelCategory: Must be one of "TOPWEAR", "BOTTOMWEAR", or "FOOTWEAR"
         2. apparelProfile: Must be "FEMALE"
         3. apparelType: 
            - Clothing: tops, tshirts, shirts, blouses, sweaters, sweatshirts, jackets, coats, blazers, shrug, kurtas, kurtis, dupatta, tunics, shawl, lehenga_choli, nehru_jackets, waistcoat, rain_jacket, dress, saree, skirt, jumpsuit, playsuit, coords, jeans, casual_trouser, formal_trouser, shorts, track_pants, legging, palazzo
            - Footwear: sports_shoes, boots, heels, casual_shoes, flats, mules, sandals, pumps, gladiators, one_toe_flats, open_toe_flats, t_strap_flats, mojaris, ballerinas, peep_toes, mary_janes, boat_shoes, brogues, clogs, derbys, driving_shoes, espadrilles, flatforms, loafers, monks, mule_sneakers, oxfords, skate_shoes, slip_on_sneakers, sneakers, trekking_shoes, biker_boots, chelsea_boots, chunky_boots, cowboy_boots, desert_boots, hiking_boots, monk_straps, rain_boots, regular_boots, slouchy_boots, winter_boots
         4. pattern: solid, geometric, floral, washed, striped, self_design, abstract, ethnic_motifs, woven_design, colourblocked, bandhani, tribal, embellished, checked, paisley, brand_logo, typography, animal, ombre, graphic, camouflage, conversational, polka_dots, houndstooth, chevron, leheriya, quirky, tie_and_dye, candy_stripes, cartoon_characters, dyed, painted, sequinned_stripes, tropical, vertical_stripes, diagonal, big_prints, small_prints
         5. fit: 
            - Clothing: regular_fit, relaxed_fit, boxy, oversized, slim_fit, a_line, flared, bootcut, skinny, tapered, straight, mom_fit, wide
            - Footwear: slim_fit, regular_fit, tailored_fit
         6. fabric: 
            - Clothing: cotton, nylon, polyester, pure_cotton, denim, viscose_rayon, liva, linen, chanderi, wool, crepe, silk, velvet, fleece, pu, leather, modal, blended, corduroy, georgette, silk_blend, satin, acrylic, suede, chiffon, elastane, organza, synthetic, knit, jacquard, linen_blend, viscose_blend, cotton_blend, chambray, crochet, dobby, net, tweed, canvas, cashmere, khadi
            - Footwear: suede, pu, mesh, synthetic, textile, leather, canvas, synthetic_leather, synthetic_patent, synthetic_suede, velvet, nubuck, patent_leather, croslite, plastic, rubber, fabric, fur, faux_fur, lace
         7. sleeveType: regular_sleeves, flared_sleeves, puff_sleeves, no_sleeves, roll_up_sleeves, shoulder_straps, bell_sleeves, cold_shoulder_sleeves, extended_sleeves, cap_sleeves, accordion_pleated, batwing, bishop, cape, cold_shoulder, cuffed, flutter, kimono, raglan, slit, ruffle
         8. sleeves: long, short, sleeveless, three_quarter, no_sleeves, regular, shoulder_straps
         9. neckLineORCollar: round_neck, mandarin_collar, v_neck, band_collar, tie_up_neck, keyhole_neck, shirt_collar, shoulder_straps, sweetheart_neck, boat_neck, square_neck, stylised_neck, scoop_neck, u_neck, halter_neck, turtle_neck, off_shoulder, shawl_collar, mock_collar, hood, cowl_neck, lapel_collar, henley_neck, peter_pan_collar, high_neck, shawl_neck, crew, jewel_neck, choker, one_shoulder, polo
         10. length: regular, crop, longline, mini, midi, maxi, above_knee, knee_length, high_low
         11. waistRise: high_rise, mid_rise, low_rise
         12. transparency: opaque, sheer, semi_sheer
         13. stretch: stretchable, non_stretchable
         14. ankleLength: regular, mid_top, high_top
         15. fastening: ankle_loop, backstrap, buckles, lace_ups, no_back_strap, slip_ons, velcro, zip
         16. toeType: open_toe, peep_toe, pointed_toe, round_toe, square_toe
         17. heelType: block, comfort, flatform, kitten, platform, slim, stilleto, wedge
         18. insole: eva_or_rubber, comfort_insole, padded, leather, memory_foam, arch_support, support_insole, poron, custom_orthotics, athletic, fur`
      }
      
      Instructions:
      1. First determine the apparelCategory based on the type of clothing
      2. Only include attributes that are clearly visible in the image
      3. Use exact values from the provided lists
      4. Skip any attributes that are ambiguous or not clearly visible
      5. Return a JSON response with only the confirmed attributes
      6. Include the apparelProfile field with the correct value ("MALE" or "FEMALE")
      7. For jackets, also include the jacketType field if applicable
      8. For female clothing, include both sleeveType and sleeves if visible
      9. For male clothing, include both neckLineORCollar and shape if visible
      10. For footwear:
          - Set apparelCategory to "FOOTWEAR"
          - Skip attributes that are not applicable to footwear (sleeves, neckLineORCollar, etc.)
          - Focus on attributes that are relevant to footwear (pattern, fabric, etc.)
          - For boots, specify the exact boot type from the list
          - For shoes, specify the exact shoe type from the list
          - Include footwear-specific attributes (ankleLength, fastening, toeType, heelType, insole) when visible
          - Use footwear-specific fit and fabric types when applicable
      11. For tops:
          - Set apparelCategory to "TOPWEAR"
          - Include relevant top attributes (sleeves, neckLineORCollar, etc.)
      12. For bottoms:
          - Set apparelCategory to "BOTTOMWEAR"
          - Include relevant bottom attributes (waistRise, length, etc.)
      13. IMPORTANT: Use the exact string values for apparelCategory and apparelProfile as specified above`;

      console.log(
        "[SearchService] Sending attribute extraction request to Gemini"
      );
      const response = await this.aiService.getGeminiImageAnalysis(
        imageUrl,
        "Extract clothing attributes",
        systemPrompt
      );

      console.log(
        "[SearchService] Received attribute extraction response:",
        response
      );

      // Extract JSON from the response
      let jsonString = response;
      const jsonMatch = response.match(/```json([\s\S]*?)```/);
      if (jsonMatch) {
        jsonString = jsonMatch[1].trim();
      }

      // Parse the JSON manually
      const parsedData = JSON.parse(jsonString);
      console.log("[SearchService] Parsed attributes:", parsedData);
      return parsedData;
    } catch (error) {
      console.error("[SearchService] Error extracting attributes:", {
        error,
        imageUrl,
        gender,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw new Error("Failed to extract attributes");
    }
  }

  private convertToSearchQuery(data: any): string {
    const queryParts = [];

    // Add category and profile
    if (data.apparelCategory)
      queryParts.push(data.apparelCategory.toLowerCase());

    // Add apparel type
    if (data.apparelType) {
      // Convert snake_case to spaces
      queryParts.push(data.apparelType.replace(/_/g, " "));
    }

    // Add other attributes
    if (data.pattern) queryParts.push(data.pattern);
    if (data.fabric) queryParts.push(data.fabric);
    if (data.color) queryParts.push(data.color);
    if (data.toeType) {
      // Convert snake_case to spaces
      queryParts.push(data.toeType.replace(/_/g, " "));
    }

    // Join all parts with spaces
    return queryParts.join(" ");
  }

  async searchByImage(
    imageUrl: string,
    gender: ApparelProfile
  ): Promise<{
    valid: boolean;
    error?: string;
    attributes?: Partial<ApparelMetaData>;
    items: any[];
    totalCount: number;
  }> {
    try {
      console.log("[SearchService] Starting image search:", {
        imageUrl,
        gender,
      });

      // First validate the image
      console.log("[SearchService] Validating image");
      const validation = await this.validateImage(imageUrl);
      console.log("[SearchService] Validation result:", validation);

      if (!validation.valid) {
        console.log(
          "[SearchService] Image validation failed, returning error response"
        );
        return {
          valid: false,
          error: validation.error || "Invalid image",
          items: [],
          totalCount: 0,
        };
      }

      // Extract attributes from the image
      console.log("[SearchService] Extracting attributes");
      const attributes = await this.extractAttributes(imageUrl, gender);
      console.log("[SearchService] Extracted attributes:", attributes);

      // Convert attributes to search query
      const searchQuery = this.convertToSearchQuery(attributes);
      console.log("[SearchService] Generated search query:", searchQuery);

      gender = gender.toUpperCase() as ApparelProfile;

      const apparelProfile = gender === "FEMALE" ? "Women" : "Men";

      // Search for the item using the query
      const searchResult = await this.apparelCollectionService.searchApparels(
        searchQuery,
        undefined,
        1, // page
        1, // limit to 1 result
        apparelProfile
      );

      const result = {
        valid: true,
        attributes,
        items: searchResult.results,
        totalCount: searchResult.total,
      };

      console.log("[SearchService] Search completed successfully:", result);
      return result;
    } catch (error) {
      console.error("[SearchService] Error in searchByImage:", {
        error,
        imageUrl,
        gender,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw new Error("Failed to perform image search");
    }
  }
}
