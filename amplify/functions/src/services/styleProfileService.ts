import { StyleProfileDao } from "../db/styleProfileDao";
import { ImageAnalysisUtil } from "../utils/imageAnalysisUtil";
import {
  PromptTemplate,
  curationTemplates,
  reviewTemplates,
  wardrobeTemplates,
} from "../../models/Prompts";

import { getStyleGuideResponse } from "../../models/StyleGuide";
import { CreateStyleProfileInput, StyleProfileStore, UpdateStyleProfileInput, Undertone, AnalyzeImageResponse, StyleGuideResponse, SelfieAnalysisResponse, Season } from "../types/style.types";

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// Custom error for service layer
export class StyleProfileServiceError extends Error {
  constructor(
    message: string,
    public readonly errorCode: string = "UNKNOWN",
    public readonly statusCode: number = 500
  ) {
    super(message);
    this.name = "StyleProfileServiceError";
  }
}

export class StyleProfileService {
  private dao: StyleProfileDao;
  private imageAnalysisUtil: ImageAnalysisUtil;

  constructor() {
    this.dao = new StyleProfileDao();
    this.imageAnalysisUtil = new ImageAnalysisUtil();
  }

  async createProfile(
    input: CreateStyleProfileInput
  ): Promise<StyleProfileStore> {
    console.log("[StyleProfileService][createProfile] Creating profile:", {
      userId: input.userId,
    });

    try {
      // Check if user already has a profile
      const existingProfile = await this.dao.getProfile(input.userId);
      if (existingProfile) {
        console.warn(
          "[StyleProfileService][createProfile] Profile already exists:",
          {
            userId: input.userId,
          }
        );
        throw new StyleProfileServiceError(
          `Profile already exists for user: ${input.userId}`,
          "PROFILE_EXISTS",
          409
        );
      }

      const profile = await this.dao.createProfile(input);
      console.log(
        "[StyleProfileService][createProfile] Profile created successfully:",
        {
          userId: input.userId,
        }
      );
      return profile;
    } catch (error) {
      // Re-throw service errors
      if (error instanceof StyleProfileServiceError) {
        throw error;
      }

      // Handle specific DAO error messages
      if (error instanceof Error) {
        if (error.message.includes("Profile already exists")) {
          console.warn(
            "[StyleProfileService][createProfile] Profile already exists:",
            {
              userId: input.userId,
              errorMessage: error.message,
            }
          );
          throw new StyleProfileServiceError(
            `Profile already exists for user: ${input.userId}`,
            "PROFILE_EXISTS",
            409
          );
        }
      }

      // Log and re-throw unknown errors
      console.error(
        "[StyleProfileService][createProfile] Unexpected error creating profile:",
        {
          userId: input.userId,
          error,
          errorType:
            error instanceof Error ? error.constructor.name : typeof error,
          errorMessage: error instanceof Error ? error.message : String(error),
        }
      );
      throw new StyleProfileServiceError(
        "Failed to create profile due to an unexpected error",
        "INTERNAL_ERROR",
        500
      );
    }
  }

  async getProfile(userId: string): Promise<StyleProfileStore> {
    console.log("[StyleProfileService][getProfile] Getting profile:", {
      userId,
    });

    if (!userId) {
      console.warn("[StyleProfileService][getProfile] Missing userId");
      throw new StyleProfileServiceError(
        "UserId is required",
        "MISSING_USERID",
        400
      );
    }

    try {
      const profile = await this.dao.getProfile(userId);
      console.log(
        "[StyleProfileService][getProfile] Profile retrieval result:",
        {
          userId,
          found: !!profile,
        }
      );
      if (!profile) {
        console.warn("[StyleProfileService][getProfile] Profile not found:", {
          userId,
        });
        throw new StyleProfileServiceError(
          `Profile not found for user: ${userId}`,
          "PROFILE_NOT_FOUND",
          404
        );
      }
      return profile;
    } catch (error) {
      console.error(
        "[StyleProfileService][getProfile] Error retrieving profile:",
        {
          userId,
          error,
          errorType:
            error instanceof Error ? error.constructor.name : typeof error,
          errorMessage: error instanceof Error ? error.message : String(error),
        }
      );
      throw new StyleProfileServiceError(
        "Failed to retrieve profile",
        "RETRIEVAL_ERROR",
        500
      );
    }
  }

  async updateProfile(
    userId: string,
    updates: UpdateStyleProfileInput
  ): Promise<StyleProfileStore> {
    console.log("[StyleProfileService][updateProfile] Updating profile:", {
      userId,
      updateFields: Object.keys(updates),
    });

    if (!userId) {
      console.warn("[StyleProfileService][updateProfile] Missing userId");
      throw new StyleProfileServiceError(
        "UserId is required",
        "MISSING_USERID",
        400
      );
    }

    if (!updates || Object.keys(updates).length === 0) {
      console.warn(
        "[StyleProfileService][updateProfile] No update fields provided:",
        { userId }
      );
      throw new StyleProfileServiceError(
        "No update fields provided",
        "MISSING_UPDATES",
        400
      );
    }

    try {
      // Check if profile exists
      const existing = await this.dao.getProfile(userId);
      if (!existing) {
        console.warn(
          "[StyleProfileService][updateProfile] Profile not found:",
          { userId }
        );
        throw new StyleProfileServiceError(
          `Profile not found for user: ${userId}`,
          "PROFILE_NOT_FOUND",
          404
        );
      }

      const updatedProfile = await this.dao.updateProfile(userId, updates);
      console.log(
        "[StyleProfileService][updateProfile] Profile updated successfully:",
        {
          userId,
          updatedFields: Object.keys(updates),
        }
      );
      return updatedProfile;
    } catch (error) {
      // Re-throw service errors
      if (error instanceof StyleProfileServiceError) {
        throw error;
      }

      // Handle specific DAO error messages
      if (error instanceof Error) {
        if (error.message.includes("Profile does not exist")) {
          console.warn(
            "[StyleProfileService][updateProfile] Profile not found:",
            {
              userId,
              errorMessage: error.message,
            }
          );
          throw new StyleProfileServiceError(
            `Profile not found for user: ${userId}`,
            "PROFILE_NOT_FOUND",
            404
          );
        }
      }

      console.error(
        "[StyleProfileService][updateProfile] Error updating profile:",
        {
          userId,
          error,
          errorType:
            error instanceof Error ? error.constructor.name : typeof error,
          errorMessage: error instanceof Error ? error.message : String(error),
        }
      );
      throw new StyleProfileServiceError(
        "Failed to update profile",
        "UPDATE_ERROR",
        500
      );
    }
  }

  async deleteProfile(userId: string): Promise<void> {
    console.log("[StyleProfileService][deleteProfile] Deleting profile:", {
      userId,
    });

    if (!userId) {
      console.warn("[StyleProfileService][deleteProfile] Missing userId");
      throw new StyleProfileServiceError(
        "UserId is required",
        "MISSING_USERID",
        400
      );
    }

    try {
      // Check if profile exists
      const existing = await this.dao.getProfile(userId);
      if (!existing) {
        console.warn(
          "[StyleProfileService][deleteProfile] Profile not found:",
          { userId }
        );
        throw new StyleProfileServiceError(
          `Profile not found for user: ${userId}`,
          "PROFILE_NOT_FOUND",
          404
        );
      }

      await this.dao.deleteProfile(userId);
      console.log(
        "[StyleProfileService][deleteProfile] Profile deleted successfully:",
        { userId }
      );
    } catch (error) {
      // Re-throw service errors
      if (error instanceof StyleProfileServiceError) {
        throw error;
      }

      // Handle specific DAO error messages
      if (error instanceof Error) {
        if (error.message.includes("Profile does not exist")) {
          console.warn(
            "[StyleProfileService][deleteProfile] Profile not found:",
            {
              userId,
              errorMessage: error.message,
            }
          );
          throw new StyleProfileServiceError(
            `Profile not found for user: ${userId}`,
            "PROFILE_NOT_FOUND",
            404
          );
        }
      }

      console.error(
        "[StyleProfileService][deleteProfile] Error deleting profile:",
        {
          userId,
          error,
          errorType:
            error instanceof Error ? error.constructor.name : typeof error,
          errorMessage: error instanceof Error ? error.message : String(error),
        }
      );
      throw new StyleProfileServiceError(
        "Failed to delete profile",
        "DELETE_ERROR",
        500
      );
    }
  }

  async analyzeUserSelfie(
    url: string,
    userUndertone: Undertone
  ): Promise<SelfieAnalysisResponse> {
    console.log("[StyleProfileService][analyzeUserSelfie] Analyzing image:", {
      url: url.substring(0, 30) + "...", // Log partial URL for privacy
    });

    if (!url) {
      console.warn("[StyleProfileService][analyzeUserSelfie] Missing URL");
      return {
        isValid: false,
        errorMessage: "Image URL is required"
      };
    }

    try {
      // Dummy implementation - in a real app, this would call a vision API
      console.log(
        "[StyleProfileService][analyzeUserSelfie] Returning analysis result"
      );
      const imageAnalysisResponse =
        await this.imageAnalysisUtil.analyzeUserSelfie(url, userUndertone);

      console.log(
        "[StyleProfileService][analyzeUserSelfie] Image analyzed successfully:",
        {
          url: url.substring(0, 30) + "...",
          userContrast: imageAnalysisResponse.userContrast,
          userHairColor: imageAnalysisResponse.userHairColor,
          userSeason: imageAnalysisResponse?.userSeason,
          userEyeColor: imageAnalysisResponse.userEyeColor,
          userSkinTone: imageAnalysisResponse.userSkinTone,
        }
      );

      return imageAnalysisResponse;
    } catch (error) {
      console.error(
        "[StyleProfileService][analyzeUserSelfie] Error analyzing image:",
        {
          urlPrefix: url.substring(0, 30) + "...",
          error,
          errorType:
            error instanceof Error ? error.constructor.name : typeof error,
          errorMessage: error instanceof Error ? error.message : String(error),
        }
      );

      // Return error response instead of throwing
      return {
        isValid: false,
        errorMessage: error instanceof Error ? error.message : "Failed to analyze image"
      };
    }
  }

  async generateStyleGuide(
    styleProfile: CreateStyleProfileInput
  ): Promise<StyleGuideResponse> {
    console.log(
      "[StyleProfileService][generateStyleGuide] Generating style guide:",
      {
        userId: styleProfile.userId,
      }
    );

    try {
      const styleGuideResponse = await getStyleGuideResponse(
        styleProfile.userGender,
        styleProfile.userBodyType,
        styleProfile.userSeason as Season,
        styleProfile.userHeight
      );
      console.log(
        "[StyleProfileService][generateStyleGuide] Style guide generated successfully:",
        {
          userId: styleProfile.userId,
        }
      );
      return styleGuideResponse;
    } catch (error) {
      console.error(
        "[StyleProfileService][generateStyleGuide] Error generating style guide:",
        {
          userId: styleProfile.userId,
          error,
          errorType:
            error instanceof Error ? error.constructor.name : typeof error,
          errorMessage: error instanceof Error ? error.message : String(error),
        }
      );
      throw new StyleProfileServiceError(
        "Failed to generate style guide",
        "STYLE_GUIDE_ERROR",
        500
      );
    }
  }

  getPrompts(): PromptTemplate[] {
    const randomPrompts: PromptTemplate[] = [
      ...this.getRandomElements(curationTemplates, 2),
      ...this.getRandomElements(reviewTemplates, 2),
      ...this.getRandomElements(wardrobeTemplates, 2),
    ];

    return randomPrompts;
  }

  private getRandomElements<T>(array: T[], count: number): T[] {
    const shuffled = [...array].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }
}
