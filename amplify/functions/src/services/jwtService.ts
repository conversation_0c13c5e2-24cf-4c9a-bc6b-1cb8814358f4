import jwt from 'jsonwebtoken';

export class JWTService {
  /**
   * Create and sign a JWT token containing the userId
   * @param userId - The user ID to include in the JWT payload
   * @param signingKey - The key to use for signing the JWT
   * @returns Promise<string> - The signed JWT token
   */
  async createJWT(userId: string, signingKey: string): Promise<string> {
    console.log("[JWTService][createJWT] Creating JWT for user:", userId);

    try {
      const payload = {
        userId,
        iat: Math.floor(Date.now() / 1000), // Issued at time
        exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // Expires in 24 hours
      };

      const token = jwt.sign(payload, signingKey, {
        algorithm: 'HS256'
      });

      console.log("[JWTService][createJWT] JWT created successfully for user:", userId);
      return token;
    } catch (error) {
      console.error("[JWTService][createJWT] Error creating JWT:", error);
      throw new Error(`Failed to create JWT: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Verify and decode a JWT token
   * @param token - The JWT token to verify
   * @param signingKey - The key used to sign the JWT
   * @returns Promise<any> - The decoded payload
   */
  async verifyJWT(token: string, signingKey: string): Promise<any> {
    console.log("[JWTService][verifyJWT] Verifying JWT token");

    try {
      const decoded = jwt.verify(token, signingKey, {
        algorithms: ['HS256']
      });

      console.log("[JWTService][verifyJWT] JWT verified successfully");
      return decoded;
    } catch (error) {
      console.error("[JWTService][verifyJWT] Error verifying JWT:", error);
      throw new Error(`Failed to verify JWT: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
