// import { AIService } from "../integrations/AIIntegration";
// import { TypeChatService } from "../integrations/TypeChatIntegration";
// import { ApparelMetaData } from "../apparelController/types";

// interface TaggingInput {
//   apparelMediaUrl: string;
//   apparelDetails?: Record<string, any>;
// }

// export class ApparelTaggingService {
//   private aiService: AIService;
//   private typeChatService: TypeChatService;

//   constructor() {
//     this.aiService = new AIService();
//     this.typeChatService = new TypeChatService();
//   }

//   async tagApparel(input: TaggingInput): Promise<ApparelMetaData> {
//     console.log("[ApparelTagging] Processing apparel:", {
//       mediaUrl: input.apparelMediaUrl,
//     });

//     // AI prompt for image analysis
//     const systemPrompt = `You are an advanced AI fashion analyst with deep expertise in garment classification. Examine apparel images and product details with meticulous attention to tag ALL applicable attributes using the strict enum values provided.

//     ### Input Format
//     - High-quality image of the apparel
//     - Supplemental text details (if available)

//     ### Valid Enum Values
//     apparelType: "tshirt", "casual_shirt", "formal_shirt", "sweatshirt", "sweater", "jacket", "blazer", "suit", "jeans", "casual_trouser", "formal_trouser", "shorts", "track_pants", "dress", "top", "skirt", "coords", "playsuit", "jumpsuit", "shrug", "jacket_coat", "waistcoat", "kurta_set", "kurti", "saree", "legging", "lehanga_set", "dupatta", "kurta", "palazzo"

//     pattern: "checked", "colorblocked", "dyed", "embellished", "embroidered", "painted", "solid", "horizontal_stripes", "woven_design", "self_design", "abstract", "animal", "candy_stripes", "cartoon_characters", "conversational", "ethnic_motif", "geometric", "ombre", "polka_dots", "sequinned_stripes", "tie_and_dye", "tribal", "tropical", "typography", "vertical_stripes", "diagonal", "big_prints", "small_prints", "chevron", "floral"

//     colour: "warm_shades", "cool_shades", "cool_pastels", "monochromes", "neutrals", "warm_pastels"

//     fit: "slim", "regular", "a_line", "flared", "boot_cut", "skinny", "tapered", "oversized", "straight", "mom", "relaxed", "wide"

//     length: "crop", "longline", "regular", "high_low", "maxi", "mini", "midi", "knee_length", "above_knee"

//     fabric: "blended", "chanderi", "chiffon", "cotton", "elastane", "silk", "crepe", "georgette", "organza", "linen", "polyester", "synthetic", "viscose_rayon", "knit", "jacquard", "leather", "linen_blend", "pu", "wool", "viscose_blend", "silk_blend", "denim", "modal", "cotton_blend", "chambray", "corduroy", "crochet", "dobby", "satin", "net", "tweed", "velvet", "canvas", "cashmere", "khadi"

//     sleeves: "long", "short", "sleeveless", "three_quarter", "no_sleeves", "regular", "shoulder_straps", "accordion_pleated", "batwing", "bell", "bishop", "cap", "cape", "cold_shoulder", "cuffed", "extended", "flared", "flutter", "kimono", "puff", "raglan", "roll_up", "slit", "ruffle"

//     neckLineORCollar: "crew", "round", "band", "boat", "cowl", "halter", "keyhole", "mandarin", "scoop", "shirt", "shawl", "shoulder_straps", "square_neck", "stylised", "sweetheart", "tie_up", "polo", "choker", "hood", "off_shoulder", "one_shoulder", "peter_pan", "jewel_neck", "lapel"

//     shape: "a_line", "anarkali", "straight", "kaftan", "bodycon", "drop_waist", "empire", "fit_flare", "sheath", "pinafore", "peplum", "structured"

//     fade: "light_fade", "dark_wash", "heavy_fade"

//     stretch: "stretchable", "non_stretchable"

//     waistRise: "high_rise", "mid_rise", "low_rise"

//     transparency: "sheer", "opaque", "semi_sheer"
    
//     vibe: "bold", "playful", "casual", "edgy", "minimal", "ethnic", "bohemian", "sporty", "elegant", "professional"

//     ### Critical Requirements
//     - Use ONLY the exact enum values listed above
//     - Perform layer-by-layer analysis: Base garment first, then details
//     - Resolve conflicts: Prioritize image evidence over text descriptions
//     - Include attributes ONLY when clearly identifiable

//     ### Output Format
//     Strict JSON with identified attributes using EXACT enum values. No nulls/placeholders.

//     Example Output:
//     {
//     "apparelType": "formal_shirt",
//     "pattern": "vertical_stripes",
//     "colour": "cool_shades",
//     "fit": "slim",
//     "length": "regular",
//     "fabric": "cotton_blend",
//     "sleeves": "regular",
//     "neckLineORCollar": "mandarin",
//     "shape": "structured",
//     "fade": "dark_wash",
//     "transparency": "opaque",
//     "waistRise": "mid_rise",
//     "stretch": "non_stretchable",
//     "vibe": "professional"
//     }  

//     Return ONLY valid JSON. No explanations. No partial responses.`;

//     try {
//       // Get AI analysis
//       const aiResponse = await this.aiService.getGeminiImageAnalysis(
//         input.apparelMediaUrl,
//         JSON.stringify(input.apparelDetails || {}),
//         systemPrompt
//       );

//       console.log("[ApparelTagging] Received AI response:", {
//         response: aiResponse,
//       });

//       const parsedResponse = await this.typeChatService.parseApparelMetadata(
//         JSON.stringify(aiResponse)
//       );

//       const result: ApparelMetaData = {
//         ...parsedResponse,
//         apparelMediaUrl: input.apparelMediaUrl,
//       };

//       console.log("[ApparelTagging] Successfully analyzed apparel:", {
//         attributes: Object.keys(result),
//       });

//       return result;
//     } catch (error) {
//       console.error("[ApparelTagging] Error analyzing apparel:", {
//         error,
//       });
//       throw error;
//     }
//   }
// }
