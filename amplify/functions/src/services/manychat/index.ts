import { ManychatStyleService } from './manychat.style';
import { ManychatCommonService } from './manychat.common';
import { ManychatOutfitService } from './manychat.outfit';
import { ManychatApparelService } from './manychat.apparel';
import { ManychatWeatherService } from './manychat.weather';
import { ManychatUtilService } from './manychat.util';

// Export all Manychat services
export {
  ManychatStyleService,
  ManychatCommonService,
  ManychatOutfitService,
  ManychatApparelService,
  ManychatWeatherService,
  ManychatUtilService
};

// Create instances for direct import
export const manychatStyleService = new ManychatStyleService();
export const manychatCommonService = new ManychatCommonService();
export const manychatOutfitService = new ManychatOutfitService();
export const manychatApparelService = new ManychatApparelService();
export const manychatWeatherService = new ManychatWeatherService();
export const manychatUtilService = new ManychatUtilService();
