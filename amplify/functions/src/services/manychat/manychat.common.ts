import { ManychatClient } from '../../external/manychat/ManychatClient';
import { ManychatFieldValue } from '../../external/manychat/types';
import { ImageStorageService } from '../imageStorageService';

/**
 * Common utilities for Manychat services
 */
export class ManychatCommonService {
  private manychatClient: ManychatClient;
  private imageStorageService: ImageStorageService;

  constructor() {
    this.manychatClient = new ManychatClient({
      apiKey: process.env.MANYCHAT_API_KEY || '1489932:2b3016291ca91ee795d942a89aa5b362'
    });
    this.imageStorageService = new ImageStorageService();
  }

  /**
   * Set custom fields for a Manychat subscriber
   * @param subscriberId The subscriber ID
   * @param fields The fields to set
   * @returns The Manychat response
   */
  async setCustomFields(
    subscriberId: number,
    fields: Array<{ field_id: number; field_value: ManychatFieldValue }>
  ): Promise<any> {
    try {
      console.log(`[ManychatCommonService][setCustomFields] Setting custom fields for subscriber ${subscriberId}`, {
        fields: JSON.stringify(fields)
      });
      
      const response = await this.manychatClient.setCustomFields(subscriberId, fields);
      
      console.log(`[ManychatCommonService][setCustomFields] Custom fields set successfully for subscriber ${subscriberId}`);
      return response;
    } catch (error) {
      console.error(`[ManychatCommonService][setCustomFields] Error setting custom fields for subscriber ${subscriberId}:`, error);
      throw error;
    }
  }

  /**
   * Convert a Manychat image URL to an Azure storage URL
   * @param imageUrl The Manychat image URL
   * @param identifier A unique identifier for the image
   * @returns The Azure storage URL
   */
  async convertImageUrl(imageUrl: string, identifier: string): Promise<string> {
    try {
      console.log(`[ManychatCommonService][convertImageUrl] Converting image URL for ${identifier}`, {
        imageUrl: imageUrl.substring(0, 30) + "..." // Log partial URL for privacy
      });
      
      const azureUrl = await this.imageStorageService.uploadImage(imageUrl, identifier);
      
      console.log(`[ManychatCommonService][convertImageUrl] Image URL converted successfully for ${identifier}`, {
        azureUrl: azureUrl.substring(0, 30) + "..." // Log partial URL for privacy
      });
      
      return azureUrl;
    } catch (error) {
      console.error(`[ManychatCommonService][convertImageUrl] Error converting image URL for ${identifier}:`, error);
      throw error;
    }
  }
}
