import { ManychatCommonService } from './manychat.common';
import { ManychatFieldValue } from '../../external/manychat/types';
import { MANYCHAT_FIELD_IDS } from '../../external/manychat/constants';
import { UserWardrobeInfo, CreateApparelsFromImagesResponse, ApparelCategory, ApparelStatus } from '../../types/apparel.types';
import { ApparelService } from '../apparelService';
import {
  ManychatCreateApparelRequest,
  ManychatCreateApparelResponse,
  ManychatGetApparelsByCategoryRequest as ManychatGetApparelsRequest
} from '../../types/manychat';
import { ImageCollageService } from '../imageCollageService';

/**
 * Service for handling apparel-related operations in Manychat
 */
export class ManychatApparelService {
  private manychatCommonService: ManychatCommonService;
  private apparelService: ApparelService;
  private imageCollageService: ImageCollageService;

  constructor() {
    this.manychatCommonService = new ManychatCommonService();
    this.apparelService = new ApparelService();
    this.imageCollageService = new ImageCollageService();
  }

  /**
   * Create multiple apparels from images
   * This method processes each image in sequence, adding successfully created apparels
   * to the existingApparels array to prevent duplicates across images in the same batch.
   *
   * @param request The request containing userId, manychatSubscriberId, gender, and imageUrls
   * @returns Response with success status, created apparels, and any duplicates
   */
  async createApparels(
    request: ManychatCreateApparelRequest
  ): Promise<ManychatCreateApparelResponse> {
    try {
      const { userId, gender, imageUrls, manychatSubscriberId } = request;

      console.log(`[ManychatApparelService] Starting apparel creation for user ${userId}`, {
        imageCount: imageUrls.length,
        gender
      });

      // Use the generic method from apparelService to process the images
      const results = await this.apparelService.createApparelsFromImages({
        userId,
        imageUrls,
        gender
      });

      // Update overall success status
      if (results.successfullyProcessed === 0) {
        results.success = false;
        results.message = results.duplicates.length > 0 ?
          'All images contained duplicate apparels or failed processing' :
          'Failed to process any images';
        console.log(`[ManychatApparelService] No images were successfully processed`);
      } else if (results.successfullyProcessed < results.processedImages) {
        results.message = `Successfully processed ${results.successfullyProcessed} out of ${results.processedImages} images`;
        console.log(`[ManychatApparelService] Partially successful: ${results.successfullyProcessed}/${results.processedImages} images processed`);
      } else {
        results.message = `Successfully processed all ${results.processedImages} images`;
        console.log(`[ManychatApparelService] Successfully processed all ${results.processedImages} images`);
      }

      // Get the latest apparels for updating Manychat summary fields
      const existingApparels = await this.apparelService.listApparels(userId);

      // Update Manychat summary fields at the end of processing
      try {
        console.log(`[ManychatApparelService] Updating Manychat summary fields`);
        await this.updateManychatSummaryFields(userId, manychatSubscriberId, existingApparels, results);
        console.log(`[ManychatApparelService] Successfully updated Manychat summary fields`);
      } catch (error) {
        console.error(`[ManychatApparelService] Error updating Manychat summary fields:`, {
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        // Don't fail the entire operation if summary field updates fail
      }

      return results;
    } catch (error) {
      console.error(`[ManychatApparelService] Error creating apparels:`, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create apparels',
        apparels: [],
        duplicates: [],
        processedImages: 0,
        successfullyProcessed: 0,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Update Manychat summary fields at the end of apparel creation process
   * This method sets the following fields:
   * - TOTAL_TOPWEAR_COUNT: Total count of topwear items
   * - TOTAL_BOTTOMWEAR_COUNT: Total count of bottomwear items
   * - TOTAL_FOOTWEAR_COUNT: Total count of footwear items
   * - TOTAL_ITEM_COUNT: Total count of all items
   * - APPAREL_ADDITION_STATUS: SUCCESS, TOTAL_FAILURE, or SUCCESS_WITH_DUPLICATES
   * - APPAREL_ADDITION_ERROR_MESSAGE: Error message if applicable
   * - APPAREL_ADDITION_DUPLICATE_ITEMS: Message about duplicate items
   * - APPAREL_ADDITION_COLLAGE_TEMP: URL of a collage of successfully added apparels
   *
   * @param userId The user ID
   * @param manychatSubscriberId The Manychat subscriber ID
   * @param existingApparels The user's existing apparels
   * @param results The results of the apparel creation process
   */
  private async updateManychatSummaryFields(
    userId: string,
    manychatSubscriberId: number,
    existingApparels: UserWardrobeInfo[],
    results: CreateApparelsFromImagesResponse
  ): Promise<void> {
    try {
      console.log(`[ManychatApparelService] Updating summary fields for user ${userId}`);

      // Count apparels by category
      const categoryCounts = existingApparels.reduce(
        (acc, item) => {
          if (!item.apparel || !item.apparel.apparelCategory) return acc;

          switch (item.apparel.apparelCategory.toUpperCase()) {
            case 'TOPWEAR':
              acc.topwear++;
              break;
            case 'BOTTOMWEAR':
              acc.bottomwear++;
              break;
            case 'FOOTWEAR':
              acc.footwear++;
              break;
          }
          return acc;
        },
        { topwear: 0, bottomwear: 0, footwear: 0 }
      );

      // Determine status
      let status: string;
      let errorMessage: string = '';
      let duplicateItemsMessage: string = '';

      if (results.successfullyProcessed === 0) {
        status = 'TOTAL_FAILURE';
        errorMessage = results.message;
      } else if (results.duplicates && results.duplicates.length > 0) {
        status = 'SUCCESS_WITH_DUPLICATES';

        // Create a message about duplicate items
        duplicateItemsMessage = results.duplicates
          .map(dup => {
            const descriptions = dup.apparelDescriptions.join(', ');
            return `${descriptions}`;
          })
          .join('. ');
      } else {
        status = 'SUCCESS';
      }

      // Prepare fields to update
      const customFields: Array<{ field_id: number; field_value: ManychatFieldValue }> = [
        {
          field_id: MANYCHAT_FIELD_IDS.TOTAL_TOPWEAR_COUNT,
          field_value: categoryCounts.topwear
        },
        {
          field_id: MANYCHAT_FIELD_IDS.TOTAL_BOTTOMWEAR_COUNT,
          field_value: categoryCounts.bottomwear
        },
        {
          field_id: MANYCHAT_FIELD_IDS.TOTAL_FOOTWEAR_COUNT,
          field_value: categoryCounts.footwear
        },
        {
          field_id: MANYCHAT_FIELD_IDS.TOTAL_ITEM_COUNT,
          field_value: existingApparels.length
        },
        {
          field_id: MANYCHAT_FIELD_IDS.APPAREL_ADDITION_STATUS,
          field_value: status
        }
      ];

      // Add error message if applicable
      if (errorMessage) {
        customFields.push({
          field_id: MANYCHAT_FIELD_IDS.APPAREL_ADDITION_ERROR_MESSAGE,
          field_value: errorMessage
        });
      }

      // Add duplicate items message if applicable
      if (duplicateItemsMessage) {
        customFields.push({
          field_id: MANYCHAT_FIELD_IDS.APPAREL_ADDITION_DUPLICATE_ITEMS,
          field_value: duplicateItemsMessage
        });
      }

      // Create a collage of successfully added apparels if any
      if (results.apparels && results.apparels.length > 0) {
        try {
          const apparelImages = results.apparels.map(item => item.apparel.apparelMediaUrl);

          console.log(`[ManychatApparelService] Creating collage with ${apparelImages.length} images`);

          const collageUrl = await this.imageCollageService.createAndStoreCollage(apparelImages, userId);

          console.log(`[ManychatApparelService] Successfully created collage`);

          customFields.push({
            field_id: MANYCHAT_FIELD_IDS.APPAREL_ADDITION_COLLAGE_TEMP,
            field_value: collageUrl
          });
        } catch (collageError) {
          console.error(`[ManychatApparelService][updateManychatSummaryFields] Error creating collage:`, {
            error: collageError instanceof Error ? collageError.message : 'Unknown error',
            stack: collageError instanceof Error ? collageError.stack : undefined
          });
          // Continue without the collage
        }
      }

      // Update Manychat fields
      console.log(`[ManychatApparelService][updateManychatSummaryFields] Calling setCustomFields for user ${userId}`, {
        fieldCount: customFields.length,
        fieldIds: customFields.map(field => field.field_id)
      });

      await this.manychatCommonService.setCustomFields(manychatSubscriberId, customFields);

      console.log(`[ManychatApparelService][updateManychatSummaryFields] Successfully updated summary fields for user ${userId}`);
    } catch (error) {
      console.error(`[ManychatApparelService][updateManychatSummaryFields] Error updating summary fields:`, {
        userId,
        manychatSubscriberId,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        errorType: error instanceof Error ? error.constructor.name : typeof error
      });
      throw error; // Rethrow to be caught by the caller
    }
  }

  /**
   * Get apparels by category and update Manychat fields with collages and details
   * This method:
   * 1. Retrieves apparels filtered by category (if specified) and non-archived status
   * 2. Creates collages for each category (topwear, bottomwear, footwear)
   * 3. Updates Manychat fields with collage URLs and apparel details
   *
   * @param request The request containing userId, manychatSubscriberId, and optional apparelCategory
   * @returns Object with success status and message
   */
  async getApparels(
    request: ManychatGetApparelsRequest
  ): Promise<{ success: boolean; message: string }> {
    try {
      const { userId, manychatSubscriberId, apparelCategory } = request;

      console.log(`[ManychatApparelService][getApparelsByCategory] Getting apparels for user ${userId}`, {
        apparelCategory: apparelCategory || 'all'
      });

      // Set up filters for non-archived apparels
      const filters: any = {
        apparelStatus: ApparelStatus.PRIMARY
      };

      // Add category filter if specified
      if (apparelCategory) {
        filters.apparelCategory = apparelCategory;
      }

      // Get apparels with filters
      const { items: apparels } = await this.apparelService.listApparelsWithFilters(userId, filters);

      console.log(`[ManychatApparelService][getApparelsByCategory] Found ${apparels.length} apparels`);

      // Group apparels by category
      const apparelsByCategory = {
        TOPWEAR: apparels.filter(item => item.apparel.apparelCategory === 'TOPWEAR'),
        BOTTOMWEAR: apparels.filter(item => item.apparel.apparelCategory === 'BOTTOMWEAR'),
        FOOTWEAR: apparels.filter(item => item.apparel.apparelCategory === 'FOOTWEAR')
      };

      // Prepare fields to update
      const customFields: Array<{ field_id: number; field_value: ManychatFieldValue }> = [];

      // Process each category
      for (const [category, categoryApparels] of Object.entries(apparelsByCategory)) {
        if (categoryApparels.length === 0) {
          console.log(`[ManychatApparelService][getApparelsByCategory] No apparels found for category ${category}`);
          continue;
        }

        // Create collage for this category
        try {
          const apparelImages = categoryApparels.map(item => item.apparel.apparelMediaUrl);
          console.log(`[ManychatApparelService][getApparelsByCategory] Creating collage for ${category} with ${apparelImages.length} images`);

          const collageUrl = await this.imageCollageService.createAndStoreCollage(apparelImages, userId);
          console.log(`[ManychatApparelService][getApparelsByCategory] Created collage for ${category}`);

          // Add collage URL to custom fields
          switch (category) {
            case 'TOPWEAR':
              customFields.push({
                field_id: MANYCHAT_FIELD_IDS.CURRENT_TOPWEAR_COLLAGE_URL,
                field_value: collageUrl
              });
              break;
            case 'BOTTOMWEAR':
              customFields.push({
                field_id: MANYCHAT_FIELD_IDS.CURRENT_BOTTOMWEAR_COLLAGE_URL,
                field_value: collageUrl
              });
              break;
            case 'FOOTWEAR':
              customFields.push({
                field_id: MANYCHAT_FIELD_IDS.CURRENT_FOOTWEAR_COLLAGE_URL,
                field_value: collageUrl
              });
              break;
          }

          // Create details string for this category
          const detailsString = categoryApparels.map(item => {
            const apparel = item.apparel;
            // Use productName if available, otherwise construct a description
            if (apparel.productName) {
              return apparel.productName;
            } else {
              return `${apparel.apparelType || 'Unknown type'}: ${apparel.colour || 'Unknown color'} ${apparel.pattern || ''} ${apparel.fabric || ''}`.trim();
            }
          }).join(', ');

          // Add details to custom fields
          switch (category) {
            case 'TOPWEAR':
              customFields.push({
                field_id: MANYCHAT_FIELD_IDS.CURRENT_TOPWEAR_DETAILS,
                field_value: detailsString
              });
              break;
            case 'BOTTOMWEAR':
              customFields.push({
                field_id: MANYCHAT_FIELD_IDS.CURRENT_BOTTOMWEAR_DETAILS,
                field_value: detailsString
              });
              break;
            case 'FOOTWEAR':
              customFields.push({
                field_id: MANYCHAT_FIELD_IDS.CURRENT_FOOTWEAR_DETAILS,
                field_value: detailsString
              });
              break;
          }
        } catch (error) {
          console.error(`[ManychatApparelService][getApparelsByCategory] Error processing ${category}:`, {
            error: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined
          });
          // Continue with other categories
        }
      }

      // Update Manychat fields if we have any
      if (customFields.length > 0) {
        console.log(`[ManychatApparelService][getApparelsByCategory] Updating ${customFields.length} Manychat fields`);
        await this.manychatCommonService.setCustomFields(manychatSubscriberId, customFields);
        console.log(`[ManychatApparelService][getApparelsByCategory] Successfully updated Manychat fields`);
      } else {
        console.log(`[ManychatApparelService][getApparelsByCategory] No fields to update`);
      }

      return {
        success: true,
        message: `Successfully processed apparels for user ${userId}`
      };
    } catch (error) {
      console.error(`[ManychatApparelService][getApparelsByCategory] Error:`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });

      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get apparels by category'
      };
    }
  }
}
