import { ManychatCreateStyleProfileRequest, ManychatUpdateStyleProfileRequest } from "../../types/manychat";
import {
  SelfieAnalysisResponse,
  StyleProfileStore,
  CreateStyleProfileInput,
  Undertone,
  UpdateStyleProfileInput,
  UserAge,
  SkinTone,
  BodyType,
  Height,
  EyeColor,
  HairColor,
  Contrast,
  Season
} from "../../types/style.types";
import { ImageStorageService } from "../imageStorageService";
import { StyleProfileService } from "../styleProfileService";
import { ManychatWeatherService } from "./manychat.weather";

/**
 * Service for handling style profile operations in Manychat
 */
export class ManychatStyleService {
  private styleProfileService: StyleProfileService;
  private imageStorageService: ImageStorageService;
  private manychatWeatherService: ManychatWeatherService;

  constructor() {
    this.styleProfileService = new StyleProfileService();
    this.imageStorageService = new ImageStorageService();
    this.manychatWeatherService = new ManychatWeatherService();
  }

  /**
   * Checks if a location string is in coordinates format and converts it to a city name
   * @param location The location string to check and potentially convert, can be undefined
   * @returns The city name if conversion was successful, or the original string/undefined
   * @private
   */
  private async convertCoordinatesToCity(location: string | undefined): Promise<string | undefined> {
    // If location is undefined or empty, return it as is
    if (!location) {
      return location;
    }

    try {
      // Check if the location is in "lat,lon" format (e.g., "12.8989297,77.6658305")
      const coordinatesMatch = location.match(/(-?\d+\.\d+),\s*(-?\d+\.\d+)/);

      if (coordinatesMatch) {
        console.log(`[ManychatStyleService][convertCoordinatesToCity] Location appears to be coordinates: ${location}, converting to city name`);
        const cityData = await this.manychatWeatherService.getCityFromCoordinatesString(location);
        console.log(`[ManychatStyleService][convertCoordinatesToCity] Retrieved city name: ${cityData.cityName} for coordinates: ${location}`);
        return cityData.cityName;
      }

      // If not coordinates, return the original location
      return location;
    } catch (error) {
      console.error(`[ManychatStyleService][convertCoordinatesToCity] Error converting coordinates to city:`, {
        location,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      // If there's an error in conversion, return the original location
      return location;
    }
  }

  /**
   * Create a new style profile for a Manychat user
   * @param request The request containing style profile data
   * @returns The created style profile
   */
  async createStyleProfile(request: ManychatCreateStyleProfileRequest): Promise<StyleProfileStore> {
    try {
      console.log(`[ManychatStyleService][createStyleProfile] Creating profile for user ${request.userId}`, {
        request: JSON.stringify(request)
      });

      // Convert profile image URL if provided
      let profileImageUrl = request.userProfileImageUrl;
      if (profileImageUrl) {
        profileImageUrl = await this.imageStorageService.uploadImage(
          profileImageUrl,
          `user-profile-${request.userId}`
        );
        console.log(`[ManychatStyleService][createStyleProfile] Converted profile image URL for user ${request.userId}`, {
          convertedUrl: profileImageUrl.substring(0, 30) + "..." // Log partial URL for privacy
        });
      }

      // Check for missing required fields
      // Only include non-selfie-derivable fields as required
      const requiredFields = [
        "userAge",
        "userBodyType",
        "userHeight"
      ];

      // Selfie-derivable fields - these are optional if no profile image is provided
      const selfieDerivableFields = [
        "userSkinTone",
        "userEyeColor",
        "userHairColor",
        "userContrast",
        "userSeason"
      ];

      // Check for missing required fields
      const missingRequiredFields = requiredFields.filter(field =>
        !(field in request) || !request[field as keyof ManychatCreateStyleProfileRequest]
      );

      // Check for missing selfie-derivable fields
      const missingSelfieDerivableFields = selfieDerivableFields.filter(field =>
        !(field in request) || !request[field as keyof ManychatCreateStyleProfileRequest]
      );

      // Initialize selfie analysis results
      let selfieAnalysis: SelfieAnalysisResponse | null = null;

      // First check if there are any missing required fields
      if (missingRequiredFields.length > 0) {
        console.error(`[ManychatStyleService][createStyleProfile] Missing required fields: ${missingRequiredFields.join(", ")}`);
        throw new Error(`Missing required fields for profile creation: ${missingRequiredFields.join(", ")}. These fields must be provided.`);
      }

      // If we have missing selfie-derivable fields and a profile image, try to analyze the selfie
      if (missingSelfieDerivableFields.length > 0 && profileImageUrl) {
        console.log(`[ManychatStyleService][createStyleProfile] Analyzing selfie to get missing fields: ${missingSelfieDerivableFields.join(", ")}`);

        // Analyze the selfie to get the missing fields
        try {
          selfieAnalysis = await this.styleProfileService.analyzeUserSelfie(
            profileImageUrl,
            request.userUndertone
          );

          if (!selfieAnalysis.isValid) {
            console.error(`[ManychatStyleService][createStyleProfile] Failed to analyze selfie: ${selfieAnalysis.errorMessage}`);
            console.log(`[ManychatStyleService][createStyleProfile] Proceeding without selfie-derivable fields: ${missingSelfieDerivableFields.join(", ")}`);
            // Don't throw an error, just continue without the selfie-derivable fields
          } else {
            console.log(`[ManychatStyleService][createStyleProfile] Using selfie analysis to fill in missing fields`);
          }
        } catch (analysisError) {
          console.error(`[ManychatStyleService][createStyleProfile] Error analyzing selfie:`, analysisError);
          console.log(`[ManychatStyleService][createStyleProfile] Proceeding without selfie analysis for fields: ${missingSelfieDerivableFields.join(", ")}`);
          // Don't throw an error, just continue without the selfie-derivable fields
        }
      } else if (missingSelfieDerivableFields.length > 0) {
        // We have missing selfie-derivable fields and no profile image
        console.log(`[ManychatStyleService][createStyleProfile] Missing selfie-derivable fields but no profile image provided. Proceeding without these fields: ${missingSelfieDerivableFields.join(", ")}`);
      }

      // Convert baseLocation from coordinates to city name if needed
      let baseLocation = request.baseLocation;
      if (baseLocation) {
        console.log(`[ManychatStyleService][createStyleProfile] Checking if baseLocation is in coordinates format: ${baseLocation}`);
        baseLocation = await this.convertCoordinatesToCity(baseLocation);
        console.log(`[ManychatStyleService][createStyleProfile] Using baseLocation: ${baseLocation}`);
      }

      // Create the profile with all fields, using selfie analysis results if available
      const createInput: CreateStyleProfileInput = {
        userId: request.userId,
        userGender: request.userGender,
        userUndertone: request.userUndertone as Undertone,
        userAge: request.userAge as UserAge,
        userBodyType: request.userBodyType as BodyType,
        userHeight: request.userHeight as Height,
        userHeightMetric: request.userHeightMetric,
        userEyeColor: request.userEyeColor || (selfieAnalysis?.userEyeColor as EyeColor),
        userHairColor: request.userHairColor || (selfieAnalysis?.userHairColor as HairColor),
        userContrast: request.userContrast || (selfieAnalysis?.userContrast as Contrast),
        userSeason: request.userSeason || (selfieAnalysis?.userSeason as Season),
        userProfileImageUrl: profileImageUrl,
        ...(baseLocation && { baseLocation })
      };

      // Add selfie-derivable fields if they are provided or available from selfie analysis
      if (request.userSkinTone) {
        createInput.userSkinTone = request.userSkinTone as SkinTone;
      } else if (selfieAnalysis?.userSkinTone) {
        createInput.userSkinTone = selfieAnalysis.userSkinTone as SkinTone;
      }

      if (request.userEyeColor) {
        createInput.userEyeColor = request.userEyeColor as EyeColor;
      } else if (selfieAnalysis?.userEyeColor) {
        createInput.userEyeColor = selfieAnalysis.userEyeColor as EyeColor;
      }

      if (request.userHairColor) {
        createInput.userHairColor = request.userHairColor as HairColor;
      } else if (selfieAnalysis?.userHairColor) {
        createInput.userHairColor = selfieAnalysis.userHairColor as HairColor;
      }

      if (request.userContrast) {
        createInput.userContrast = request.userContrast as Contrast;
      } else if (selfieAnalysis?.userContrast) {
        createInput.userContrast = selfieAnalysis.userContrast as Contrast;
      }

      if (request.userSeason) {
        createInput.userSeason = request.userSeason as Season;
      } else if (selfieAnalysis?.userSeason) {
        createInput.userSeason = selfieAnalysis.userSeason as Season;
      }

      // Add profile image URL if provided
      if (profileImageUrl) {
        createInput.userProfileImageUrl = profileImageUrl;
      }

      // Create the profile
      const profile = await this.styleProfileService.createProfile(createInput);

      console.log(`[ManychatStyleService][createStyleProfile] Profile created successfully for user ${request.userId}${selfieAnalysis ? ' with selfie analysis' : ''}`);
      return profile;
    } catch (error) {
      console.error(`[ManychatStyleService][createStyleProfile] Error creating profile for user ${request.userId}:`, error);
      throw error;
    }
  }

  /**
   * Update an existing style profile for a Manychat user
   * @param request The request containing updated style profile data
   * @returns The updated style profile
   */
  async updateStyleProfile(request: ManychatUpdateStyleProfileRequest): Promise<StyleProfileStore> {
    try {
      console.log(`[ManychatStyleService][updateStyleProfile] Updating profile for user ${request.userId}`, {
        request: JSON.stringify(request)
      });

      // Convert profile image URL if provided
      let profileImageUrl = request.userProfileImageUrl;
      if (profileImageUrl) {
        profileImageUrl = await this.imageStorageService.uploadImage(
          profileImageUrl,
          `user-profile-${request.userId}`
        );
        console.log(`[ManychatStyleService][updateStyleProfile] Converted profile image URL for user ${request.userId}`, {
          convertedUrl: profileImageUrl.substring(0, 30) + "..." // Log partial URL for privacy
        });
      }

      // Convert baseLocation from coordinates to city name if needed
      let baseLocation = request.baseLocation;
      if (baseLocation) {
        console.log(`[ManychatStyleService][updateStyleProfile] Checking if baseLocation is in coordinates format: ${baseLocation}`);
        baseLocation = await this.convertCoordinatesToCity(baseLocation);
        console.log(`[ManychatStyleService][updateStyleProfile] Using baseLocation: ${baseLocation}`);
      }

      // Note: Selfie-derivable fields include:
      // - userSkinTone
      // - userEyeColor
      // - userHairColor
      // - userContrast
      // - userSeason

      // Initialize selfie analysis results
      let selfieAnalysis: SelfieAnalysisResponse | null = null;

      // If we have a profile image and undertone is provided, try to analyze the selfie
      if (profileImageUrl && request.userUndertone) {
        console.log(`[ManychatStyleService][updateStyleProfile] Analyzing selfie for user ${request.userId}`);

        // Analyze the selfie
        try {
          selfieAnalysis = await this.styleProfileService.analyzeUserSelfie(
            profileImageUrl,
            request.userUndertone as Undertone
          );

          if (!selfieAnalysis.isValid) {
            console.error(`[ManychatStyleService][updateStyleProfile] Failed to analyze selfie: ${selfieAnalysis.errorMessage}`);
            console.log(`[ManychatStyleService][updateStyleProfile] Proceeding without selfie analysis`);
            // Don't throw an error, just continue without the selfie analysis
          } else {
            console.log(`[ManychatStyleService][updateStyleProfile] Selfie analyzed successfully`);
          }
        } catch (analysisError) {
          console.error(`[ManychatStyleService][updateStyleProfile] Error analyzing selfie:`, analysisError);
          console.log(`[ManychatStyleService][updateStyleProfile] Proceeding without selfie analysis`);
          // Don't throw an error, just continue without the selfie analysis
        }
      }

      // Prepare input for style profile service
      const { userId, ...requestWithoutUserId } = request;
      const updateInput: UpdateStyleProfileInput = {
        ...requestWithoutUserId,
        // Only include userProfileImageUrl if it was provided in the request
        ...(profileImageUrl !== undefined && { userProfileImageUrl: profileImageUrl }),
        // Override baseLocation with the converted value if it exists
        ...(baseLocation !== undefined && { baseLocation })
      };

      // Add selfie-derivable fields from analysis if they're not already in the request
      if (selfieAnalysis?.isValid) {
        if (!request.userSkinTone && selfieAnalysis.userSkinTone) {
          updateInput.userSkinTone = selfieAnalysis.userSkinTone as SkinTone;
        }

        if (!request.userEyeColor && selfieAnalysis.userEyeColor) {
          updateInput.userEyeColor = selfieAnalysis.userEyeColor as EyeColor;
        }

        if (!request.userHairColor && selfieAnalysis.userHairColor) {
          updateInput.userHairColor = selfieAnalysis.userHairColor as HairColor;
        }

        if (!request.userContrast && selfieAnalysis.userContrast) {
          updateInput.userContrast = selfieAnalysis.userContrast as Contrast;
        }

        if (!request.userSeason && selfieAnalysis.userSeason) {
          updateInput.userSeason = selfieAnalysis.userSeason as Season;
        }
      }

      // Update the profile
      const profile = await this.styleProfileService.updateProfile(request.userId, updateInput);

      console.log(`[ManychatStyleService][updateStyleProfile] Profile updated successfully for user ${request.userId}${selfieAnalysis?.isValid ? ' with selfie analysis' : ''}`);
      return profile;
    } catch (error) {
      console.error(`[ManychatStyleService][updateStyleProfile] Error updating profile for user ${request.userId}:`, error);
      throw error;
    }
  }

  /**
   * Get a style profile for a Manychat user
   * @param userId The user ID
   * @returns The user's style profile, or null if not found
   */
  async getStyleProfile(userId: string): Promise<StyleProfileStore | null> {
    try {
      console.log(`[ManychatStyleService][getStyleProfile] Getting profile for user ${userId}`);

      // Get the profile
      const profile = await this.styleProfileService.getProfile(userId);

      console.log(`[ManychatStyleService][getStyleProfile] Profile retrieval result for user ${userId}:`, {
        found: !!profile
      });

      return profile;
    } catch (error) {
      console.error(`[ManychatStyleService][getStyleProfile] Error getting profile for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Analyze a user selfie from Manychat
   * @param userId The user ID
   * @param selfieUrl The Manychat image URL
   * @param userUndertone The user's undertone
   * @returns Analysis result with facial features and coloring
   */
  async analyzeUserSelfie(userId: string, selfieUrl: string, userUndertone: string): Promise<SelfieAnalysisResponse> {
    try {
      console.log(`[ManychatStyleService][analyzeUserSelfie] Processing selfie for user ${userId}`, {
        userUndertone,
        imageUrl: selfieUrl.substring(0, 30) + "..." // Log partial URL for privacy
      });

      // Save the image to Azure storage
      const storedImageUrl = await this.imageStorageService.uploadImage(
        selfieUrl,
        `user-selfie-${userId}`
      );

      console.log(`[ManychatStyleService][analyzeUserSelfie] Image stored for user ${userId}`, {
        storedImageUrl: storedImageUrl.substring(0, 30) + "..." // Log partial URL for privacy
      });

      // Analyze the selfie
      const analysis = await this.styleProfileService.analyzeUserSelfie(
        storedImageUrl,
        userUndertone as Undertone
      );

      console.log(`[ManychatStyleService][analyzeUserSelfie] Analysis completed for user ${userId}`, {
        isValid: analysis.isValid,
        errorMessage: analysis.errorMessage || "None"
      });

      return analysis;
    } catch (error) {
      console.error(`[ManychatStyleService][analyzeUserSelfie] Error analyzing selfie for user ${userId}:`, error);
      return {
        isValid: false,
        errorMessage: `Failed to analyze selfie: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }
}
