import { OpenWeatherService, SimpleWeatherResponse, CityLocationResponse } from "../../ai/integrations/OpenWeatherIntegration";

/**
 * Service for handling weather-related operations in Manychat
 */
export class ManychatWeatherService {
  private openWeatherService: OpenWeatherService;

  constructor() {
    this.openWeatherService = new OpenWeatherService();
  }

  /**
   * Get weather data for a location string which can be either a city name or "lat,lon" coordinates
   * @param location The location string (city name or "lat,lon" format)
   * @returns Weather data for the location
   */
  async getWeatherForLocation(location: string): Promise<SimpleWeatherResponse> {
    try {
      console.log(`[ManychatWeatherService][getWeatherForLocation] Getting weather for location: ${location}`);

      let weatherData: SimpleWeatherResponse;

      // Check if the location is in "lat,lon" format (e.g., "12.8989297,77.6658305")
      const coordinatesMatch = location.match(/(-?\d+\.\d+),\s*(-?\d+\.\d+)/);

      if (coordinatesMatch) {
        // Extract latitude and longitude
        const lat = parseFloat(coordinatesMatch[1]);
        const lon = parseFloat(coordinatesMatch[2]);

        console.log(`[ManychatWeatherService][getWeatherForLocation] Detected coordinates format: lat=${lat}, lon=${lon}`);
        weatherData = await this.openWeatherService.getWeatherByCoordinates(lat, lon);
      } else {
        // Treat as city name
        console.log(`[ManychatWeatherService][getWeatherForLocation] Treating as city name: ${location}`);
        weatherData = await this.openWeatherService.getWeatherByCity(location);
      }

      console.log(`[ManychatWeatherService][getWeatherForLocation] Weather data retrieved:`, {
        description: weatherData.description,
        location: weatherData.location
      });

      return weatherData;
    } catch (error) {
      console.error(`[ManychatWeatherService][getWeatherForLocation] Error getting weather data:`, {
        location,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get city name from coordinates string in "lat,lon" format
   * @param coordinatesString The coordinates string in "lat,lon" format
   * @returns City location data with city name and full location string
   */
  async getCityFromCoordinatesString(coordinatesString: string): Promise<CityLocationResponse> {
    try {
      console.log(`[ManychatWeatherService][getCityFromCoordinatesString] Getting city for coordinates: ${coordinatesString}`);

      // Check if the string is in "lat,lon" format (e.g., "12.8989297,77.6658305")
      const coordinatesMatch = coordinatesString.match(/(-?\d+\.\d+),\s*(-?\d+\.\d+)/);

      if (!coordinatesMatch) {
        throw new Error(`Invalid coordinates format: ${coordinatesString}. Expected format: "lat,lon"`);
      }

      // Extract latitude and longitude
      const lat = parseFloat(coordinatesMatch[1]);
      const lon = parseFloat(coordinatesMatch[2]);

      console.log(`[ManychatWeatherService][getCityFromCoordinatesString] Parsed coordinates: lat=${lat}, lon=${lon}`);

      // Call the OpenWeatherService to get city data
      const cityData = await this.openWeatherService.getCityFromCoordinates(lat, lon);

      console.log(`[ManychatWeatherService][getCityFromCoordinatesString] City data retrieved:`, {
        cityName: cityData.cityName,
        fullLocation: cityData.fullLocation
      });

      return cityData;
    } catch (error) {
      console.error(`[ManychatWeatherService][getCityFromCoordinatesString] Error getting city data:`, {
        coordinatesString,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
}
