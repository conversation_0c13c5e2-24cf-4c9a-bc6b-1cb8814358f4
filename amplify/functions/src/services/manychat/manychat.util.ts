import { AIUtil } from '../../utils/aiUtil';
import { OpenWeatherService } from '../../ai/integrations/OpenWeatherIntegration';
import { AIService } from '../../ai/integrations/AIIntegration';
import { ImageStorageService } from '../imageStorageService';
import { OUTFIT_IMAGE_GENERATION_PROMPT } from '../../ai/prompts/outfitImageGenerationPrompt';

export class ManychatUtilService {
  private aiUtil: AIUtil;
  private openWeatherService: OpenWeatherService;
  private aiService: AIService;
  private imageStorageService: ImageStorageService;

  constructor() {
    this.aiUtil = new AIUtil();
    this.openWeatherService = new OpenWeatherService();
    this.aiService = new AIService();
    this.imageStorageService = new ImageStorageService();
  }

  /**
   * Get a valid city from user input, or return ERROR if not found
   * @param req { manychatSubscriberId: string, userInput: string }
   * @returns { response: string, message: string }
   */
  async getValidCityFromText(req: { manychatSubscriberId: string, userInput: string }): Promise<{ response: string, message: string }> {
    console.log('[ManychatUtilService][getValidCityFromText] Start', { req });
    try {
      const { userInput } = req;
      console.log('[ManychatUtilService][getValidCityFromText] User input:', userInput);
      // Step 1: Extract city candidates from AIUtil
      const { cities, message: aiMessage } = await this.aiUtil.getCityCandidatesFromText(userInput);
      console.log('[ManychatUtilService][getValidCityFromText] City candidates from AI:', { cities, aiMessage });
      if (!cities || cities.length === 0) {
        console.log('[ManychatUtilService][getValidCityFromText] No city candidates found. Returning ERROR.');
        return { response: 'ERROR', message: aiMessage || "Sorry, I couldn't find any valid city in your input." };
      }
      // Step 2: Check each city with OpenWeather
      for (const city of cities.slice(0, 3)) {
        console.log(`[ManychatUtilService][getValidCityFromText] Checking city with OpenWeather: ${city}`);
        const result = await this.openWeatherService.isValidCity(city);
        console.log(`[ManychatUtilService][getValidCityFromText] OpenWeather result for '${city}':`, result);
        if (result.valid) {
          const cityString = result.city;
          console.log('[ManychatUtilService][getValidCityFromText] Found valid city:', cityString);
          return { response: cityString, message: `Found valid city: ${cityString}` };
        }
      }
      // If none are valid
      console.log('[ManychatUtilService][getValidCityFromText] No valid city found after OpenWeather checks. Returning ERROR.');
      return { response: 'ERROR', message: "Sorry, I couldn't find a valid city that matches your input." };
    } catch (error) {
      console.error('[ManychatUtilService][getValidCityFromText] Error:', error);
      return { response: 'ERROR', message: "Sorry, something went wrong while processing your request." };
    } finally {
      console.log('[ManychatUtilService][getValidCityFromText] End');
    }
  }

  /**
   * Generate an outfit image from text description and save to Azure storage
   * @param req { outfitText: string, userId: string, operationId: string }
   * @returns { imageUrl: string, message: string } or { error: string, message: string }
   */
  async generateOutfitImage(req: { outfitText: string, userId: string, operationId: string }): Promise<{ imageUrl?: string, error?: string, message: string }> {
    console.log('[ManychatUtilService][generateOutfitImage] Start', { req: { ...req, outfitText: req.outfitText.substring(0, 50) + '...' } });
    try {
      const { outfitText, userId, operationId } = req;
      console.log('[ManychatUtilService][generateOutfitImage] Generating image for outfit text:', outfitText.substring(0, 100) + '...');

      // Create the prompt using the template
      const imagePrompt = OUTFIT_IMAGE_GENERATION_PROMPT(outfitText);
      console.log('[ManychatUtilService][generateOutfitImage] Using prompt:', imagePrompt.substring(0, 150) + '...');

      // Generate image using AI service
      const generatedImageUrl = await this.aiService.generateImage(imagePrompt);
      console.log('[ManychatUtilService][generateOutfitImage] Image generated successfully');

      // Create storage path using operationId as the filename
      const storagePath = `users/${userId}/generatedOutfitImages/${operationId}`;

      // Save the generated image to Azure storage
      const storedImageUrl = await this.imageStorageService.uploadImage(generatedImageUrl, storagePath);
      console.log('[ManychatUtilService][generateOutfitImage] Image saved to Azure storage:', storedImageUrl.substring(0, 50) + '...');

      return {
        imageUrl: storedImageUrl,
        message: `Outfit image generated and saved successfully for operation ${operationId}`
      };
    } catch (error) {
      console.error('[ManychatUtilService][generateOutfitImage] Error:', error);
      return {
        error: 'ERROR',
        message: error instanceof Error ? error.message : "Sorry, something went wrong while generating the outfit image."
      };
    } finally {
      console.log('[ManychatUtilService][generateOutfitImage] End');
    }
  }
}