# Manychat Services

This directory contains modular services for interacting with Manychat. The services are organized by domain to keep files smaller and more focused.

## Structure

- `index.ts` - Exports all services and provides pre-initialized instances
- `manychat.common.ts` - Common utilities shared across all Manychat services
- `manychat.style.ts` - Style profile operations (create, update, get, analyze selfie)
- `manychat.outfit.ts` - Outfit operations (shoppable outfits, GenAI outfits)

## Usage

Import the pre-initialized service instances from the index file:

```typescript
import { manychatStyleService, manychatOutfitService } from '../services/manychat';

// Use the services
const profile = await manychatStyleService.getStyleProfile(userId);
await manychatOutfitService.setGenAIOutfit(styleProfile, context, userId);
```

## Endpoints

### Style Profile Endpoints

- `POST /style/create` - Create a new style profile
- `PUT /style/update` - Update an existing style profile
- `GET /style/get` - Get a user's style profile
- `POST /style/analyze-selfie` - Analyze a user's selfie

### Outfit Endpoints

- `POST /set-shopping-links` - Set shoppable outfit links
- `POST /set-genai-outfit` - Set GenAI-generated outfit

## Image URL Conversion

All services automatically convert Manychat image URLs to Azure storage URLs using the `ImageStorageService`. This ensures that all images are stored consistently and can be accessed by other services.
