
import {
  OutfitCollectionDao,
  UpdateOutfitCollectionDaoInput,
} from "../db/outfitCollectionDao";
import { OutfitService } from "./outfitService";
import { TypeChatService } from "../ai/integrations/TypeChatIntegration";
import { AIService } from "../ai/integrations/AIIntegration";
import { 
  EmojiElement, 
  OutfitCollection, 
  CreateOutfitCollectionInput, 
  UpdateOutfitCollectionInput, 
  OutfitStatus } from "../types/outfits.types";

interface EmojiResponse {
  selectedEmojiId: string;
}

export class OutfitCollectionService {
  private outfitCollectionDao: OutfitCollectionDao;
  private outfitService: OutfitService;
  private typeChatService: TypeChatService;
  private aiService: AIService;

  // List of emojis with their descriptions for AI to choose from
  //Note : As understood, AI will be choosing the right emoji based on the collection name that user wants to set
  //TODO : We can look at deprecating this soon. Rather, we should allow the user to pick an emoji from client side.
  private emojis: EmojiElement[] = [
    {
      emojiId: "work_office",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/1f4bc.png",
      emojiDescription: "work, office",
    },
    {
      emojiId: "art",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/1f3a8.png",
      emojiDescription: "colour palette, art, colourful",
    },
    {
      emojiId: "hot",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/1f525.png",
      emojiDescription: "fire, hot",
    },
    {
      emojiId: "rain",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/1f327.png",
      emojiDescription: "rain, rainy day, monsoon",
    },
    {
      emojiId: "winter",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/2744.png",
      emojiDescription: "winter, snow, ice, cold",
    },
    {
      emojiId: "summer",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/1f31e.png",
      emojiDescription: "summer, cool",
    },
    {
      emojiId: "gym",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/1f3cb.png",
      emojiDescription: "active, gym, work out",
    },
    {
      emojiId: "beach",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/1f3d6.png",
      emojiDescription: "beach, vacation",
    },
    {
      emojiId: "birthday",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/1f382.png",
      emojiDescription: "birthday, occasion",
    },
    {
      emojiId: "christmas",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/1f384.png",
      emojiDescription: "christmas",
    },
    {
      emojiId: "coffee",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/2615.png",
      emojiDescription: "coffee, hang out",
    },
    {
      emojiId: "sleep",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/1f634.png",
      emojiDescription: "night, sleep",
    },
    {
      emojiId: "hiking",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/1f9d7.png",
      emojiDescription: "hike, outdoor",
    },
    {
      emojiId: "diwali",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/1f386.png",
      emojiDescription: "diwali",
    },
    {
      emojiId: "romantic",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/1f498.png",
      emojiDescription: "heart, romantic, date",
    },
    {
      emojiId: "formal",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/1f935.png",
      emojiDescription: "formal, business",
    },
    {
      emojiId: "camping",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/26fa.png",
      emojiDescription: "camping, tent",
    },
    {
      emojiId: "party",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/1f389.png",
      emojiDescription: "party, celebration",
    },
    {
      emojiId: "special",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/2728.png",
      emojiDescription: "special",
    },
    {
      emojiId: "airport",
      emojiImageUrl: "https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/2708.png",
      emojiDescription: "airport, flying",
    }
  ];

  constructor() {
    this.outfitCollectionDao = new OutfitCollectionDao();
    this.outfitService = new OutfitService();
    this.typeChatService = new TypeChatService();
    this.aiService = new AIService();
  }

  private async selectEmoji(collectionName: string): Promise<EmojiElement> {
    try {
      console.log(
        "[OutfitCollectionService][selectEmoji] Selecting emoji for:",
        collectionName
      );

      // Create prompt for AI to select emoji
      const systemPrompt = `You are an assistant helping to select the most appropriate emoji for a collection name. 
        You will be provided with a collection name and a list of emoji options with descriptions.
        Your task is to determine which emoji best matches the collection name based on the descriptions provided.
        Return only the emojiId of the best match in a JSON format: { "selectedEmojiId": "emoji_id_here" }`;

      const userPrompt = JSON.stringify({
        collectionName: collectionName,
        emojiOptions: this.emojis,
      });

      // Get AI response
      const aiResponse = await this.aiService.getGeminiTextAnalysis(
        userPrompt,
        systemPrompt
      );

      console.log(
        "[OutfitCollectionService][selectEmoji] AI response:",
        aiResponse
      );

      // Parse response and validate
      let parsedResponse: EmojiResponse =
        await this.typeChatService.parseEmojiResponse(
          JSON.stringify(aiResponse)
        );

      const selectedEmojiId = parsedResponse.selectedEmojiId;

      // Find the selected emoji
      const selectedEmoji = this.emojis.find(
        (emoji) => emoji.emojiId === selectedEmojiId
      );

      if (!selectedEmoji) {
        // Fallback to default emoji if not found
        console.warn(
          "[OutfitCollectionService][selectEmoji] Emoji not found, using default"
        );
        return this.emojis[0];
      }

      console.log(
        "[OutfitCollectionService][selectEmoji] Selected emoji:",
        selectedEmoji
      );
      return selectedEmoji;
    } catch (error) {
      console.error("[OutfitCollectionService][selectEmoji] Error:", error);
      // Return default emoji in case of error
      return this.emojis[0];
    }
  }

  async createOutfitCollection(
    input: CreateOutfitCollectionInput
  ): Promise<OutfitCollection> {
    try {
      console.log(
        "[OutfitCollectionService][createOutfitCollection] Creating collection:",
        input
      );

      // Validate input
      if (!input.userId || !input.outfitCollectionName) {
        throw new Error(
          "Missing required fields: userId and outfitCollectionName"
        );
      }

      // Generate collection ID
      //TODO : keep the collectionId as the collectionName in snake-case format. This will prevent duplicate collections
      //TODO : limit the number of collections a user can have
      const outfitCollectionId = `collection_${
        Number.MAX_SAFE_INTEGER - Date.now()
      }_${input.userId}`;

      // Select emoji based on collection name
      const selectedEmoji = await this.selectEmoji(input.outfitCollectionName);

      // Create collection object
      const outfitCollection: OutfitCollection = {
        userId: input.userId,
        outfitCollectionId: outfitCollectionId,
        outfitCollectionName: input.outfitCollectionName,
        outfitCollectionMediaUrl: selectedEmoji.emojiImageUrl,
        outfitPointers: input.outfitPointers || [],
        dateCreated: new Date().toISOString(),
      };

      // Create collection in database
      const createdCollection =
        await this.outfitCollectionDao.createOutfitCollection(outfitCollection);

      // Update all outfits to include this collection ID
      //TODO : We can remove this update to outfit table since knowing an outfit is in which collection, does not help much.
      if (input.outfitPointers && input.outfitPointers.length > 0) {
        const updatePromises = input.outfitPointers.map((outfitId) =>
          this.outfitService.updateOutfit(input.userId, outfitId, {
            status: OutfitStatus.WISHLIST,
            outfitCollectionId: outfitCollectionId,
          })
        );

        await Promise.all(updatePromises);
      }

      return createdCollection;
    } catch (error) {
      console.error(
        "[OutfitCollectionService][createOutfitCollection] Error:",
        error
      );
      throw error;
    }
  }

  async getOutfitCollection(
    userId: string,
    outfitCollectionId: string
  ): Promise<OutfitCollection | null> {
    return await this.outfitCollectionDao.getOutfitCollection(
      userId,
      outfitCollectionId
    );
  }

  async listOutfitCollections(userId: string): Promise<OutfitCollection[]> {
    const { items } = await this.outfitCollectionDao.listOutfitCollections(
      userId
    );
    return items;
  }

  async updateOutfitCollection(
    userId: string,
    outfitCollectionId: string,
    updates: UpdateOutfitCollectionInput
  ): Promise<OutfitCollection> {
    try {
      console.log(
        "[OutfitCollectionService][updateOutfitCollection] Updating collection:",
        {
          userId,
          outfitCollectionId,
          updates,
        }
      );

      const daoUpdates: UpdateOutfitCollectionDaoInput = {};

      // Update collection name and select new emoji if name is updated
      if (updates.outfitCollectionName) {
        daoUpdates.outfitCollectionName = updates.outfitCollectionName;

        // Select new emoji based on updated name
        const selectedEmoji = await this.selectEmoji(
          updates.outfitCollectionName
        );
        daoUpdates.outfitCollectionMediaUrl = selectedEmoji.emojiImageUrl;
      }

      // Update in database
      return await this.outfitCollectionDao.updateOutfitCollection(
        userId,
        outfitCollectionId,
        daoUpdates
      );
    } catch (error) {
      console.error(
        "[OutfitCollectionService][updateOutfitCollection] Error:",
        error
      );
      throw error;
    }
  }

  async deleteOutfitCollection(
    userId: string,
    outfitCollectionId: string
  ): Promise<void> {
    try {
      console.log(
        "[OutfitCollectionService][deleteOutfitCollection] Deleting collection:",
        {
          userId,
          outfitCollectionId,
        }
      );

      // Get collection to get outfit pointers
      const collection = await this.outfitCollectionDao.getOutfitCollection(
        userId,
        outfitCollectionId
      );

      if (!collection) {
        throw new Error(
          `Outfit collection not found with ID: ${outfitCollectionId}`
        );
      }

      // Delete all outfits in the collection
      if (collection.outfitPointers && collection.outfitPointers.length > 0) {
        const archivePromises = collection.outfitPointers.map((outfitId) =>
          this.outfitService.updateOutfit(userId, outfitId, {
            status: OutfitStatus.ARCHIVED,
            outfitCollectionId: null,
          })
        );

        await Promise.all(archivePromises);
      }

      // Delete the collection itself
      await this.outfitCollectionDao.deleteOutfitCollection(
        userId,
        outfitCollectionId
      );

      console.log(
        "[OutfitCollectionService][deleteOutfitCollection] Collection deleted successfully"
      );
    } catch (error) {
      console.error(
        "[OutfitCollectionService][deleteOutfitCollection] Error:",
        error
      );
      throw error;
    }
  }

  async addOutfitToCollection(
    userId: string,
    outfitCollectionId: string,
    outfitId: string
  ): Promise<OutfitCollection> {
    try {
      console.log(
        "[OutfitCollectionService][addOutfitToCollection] Adding outfit to collection:",
        {
          userId,
          outfitCollectionId,
          outfitId,
        }
      );

      // Get current collection
      const collection = await this.outfitCollectionDao.getOutfitCollection(
        userId,
        outfitCollectionId
      );

      if (!collection) {
        throw new Error(
          `Outfit collection not found with ID: ${outfitCollectionId}`
        );
      }

      // Update outfit with collection ID
      await this.outfitService.updateOutfit(userId, outfitId, {
        outfitCollectionId: outfitCollectionId,
      });

      // Add outfit to collection if not already present
      if (!collection.outfitPointers.includes(outfitId)) {
        const updatedPointers = [...collection.outfitPointers, outfitId];

        return await this.outfitCollectionDao.updateOutfitCollection(
          userId,
          outfitCollectionId,
          { outfitPointers: updatedPointers }
        );
      }

      return collection;
    } catch (error) {
      console.error(
        "[OutfitCollectionService][addOutfitToCollection] Error:",
        error
      );
      throw error;
    }
  }
}
