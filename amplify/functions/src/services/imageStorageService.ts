import {
  BlobServiceClient,
  BlockBlobClient,
} from "@azure/storage-blob";
import * as fs from 'fs';

export class ImageStorageService {
  private connectionString: string;
  private blobServiceClient: BlobServiceClient;
  private containerClient;

  constructor() {
    this.connectionString =
      "DefaultEndpointsProtocol=https;AccountName=monovaoutfits;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net";
    this.blobServiceClient = BlobServiceClient.fromConnectionString(
      this.connectionString
    );
    this.containerClient =
      this.blobServiceClient.getContainerClient("ai-outfits");
  }

  async uploadImage(imageUrl: string, caseId: string): Promise<string> {
    try {
      console.log(`[Image Storage Service] Processing image: ${imageUrl}`);

      // Check if the input is a local file path or a URL
      if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
        // Handle URL
        const response = await fetch(imageUrl);
        const blob = await response.blob();
        const arrayBuffer = await blob.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        return this.uploadBuffer(buffer, caseId);
      } else {
        // Handle local file path
        return this.uploadImageFromPath(imageUrl, caseId);
      }
    } catch (error) {
      throw new Error(`Failed to upload image: ${error}`);
    }
  }

  async uploadImageFromPath(filePath: string, caseId: string): Promise<string> {
    try {
      console.log(`[Image Storage Service] Processing local file: ${filePath}`);
      
      // Read the file as a buffer
      const buffer = fs.readFileSync(filePath);
      
      return this.uploadBuffer(buffer, caseId);
    } catch (error) {
      throw new Error(`Failed to upload image from path: ${error}`);
    }
  }

  private async uploadBuffer(buffer: Buffer, caseId: string): Promise<string> {
    const blobName = `${caseId}-${Math.random()
      .toString(36)
      .substring(7)}.jpg`;
    const blockBlobClient: BlockBlobClient =
      this.containerClient.getBlockBlobClient(blobName);

    await blockBlobClient.uploadData(buffer);
    console.log("[Image Storage Service] Image uploaded successfully");

    // Return the public URL directly
    return blockBlobClient.url;
  }
} 