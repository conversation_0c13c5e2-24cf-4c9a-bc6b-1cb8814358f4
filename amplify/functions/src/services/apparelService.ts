import { ApparelVibeUtil } from "../utils/apparelVibeUtil";
import { UserWardrobeDao, WardrobeItemFilters } from "../db/userWardrobeInfoDao";
import { v4 as uuidv4 } from "uuid";
import {
  Apparel,
  ApparelMetaData,
  UserWardrobeInfo,
  ApparelSource,
  ApparelStatus,
  UpdateWardrobeInput,
  CreateApparelsFromImagesResponse,
  CreateApparelsFromImagesRequest
} from "../types/apparel.types";
import { ApparelValidator } from "../utils/apparelValidator";
import { ApparelProcessingUtil } from "../utils/apparelProcessingUtil";
import { AIService } from "../ai/integrations/AIIntegration";
import { ImageStorageService } from "./imageStorageService";
import { APPAREL_IMAGE_GENERATION_SYSTEM_PROMPT, APPAREL_IMAGE_GENERATION_USER_PROMPT } from "../ai/prompts/apparelImageGenerationPrompt";

export class ApparelService {
  private userWardrobeDao: UserWardrobeDao;
  private apparelVibeUtil: ApparelVibeUtil;
  private apparelProcessingUtil: ApparelProcessingUtil;
  private aiService: AIService;
  private imageStorageService: ImageStorageService;

  constructor() {
    this.userWardrobeDao = UserWardrobeDao.getInstance();
    this.apparelVibeUtil = new ApparelVibeUtil();
    this.apparelProcessingUtil = new ApparelProcessingUtil();
    this.aiService = new AIService();
    this.imageStorageService = new ImageStorageService();
  }

  async createApparel(
    source: ApparelSource,
    apparelMetaData: ApparelMetaData,
    userId: string,
    sourceId: string
  ): Promise<UserWardrobeInfo> {
    try {
      console.log(
        `[apparelService][createApparel] Creating apparel for user: ${userId}, source: ${source}, sourceId: ${sourceId}`
      );

      // Validate apparel metadata
      ApparelValidator.validateCreateApparelMetadata(apparelMetaData);

      // Detect and assign vibe to apparel metadata if not present
      let enrichedMetaData = apparelMetaData;
      if (!apparelMetaData.vibe) {
        enrichedMetaData = await this.apparelVibeUtil.detectAndAssignVibe(
          apparelMetaData
        );
      }

      const apparelId = this.generateApparelId(
        sourceId,
        source,
      );

      // Set apparelId in the metadata
      enrichedMetaData.apparelId = apparelId;

      console.log(
        `[apparelService][createApparel] Adding apparel to wardrobe for user: ${userId}`
      );

      const wardrobeItem = await this.userWardrobeDao.addToWardrobe(
        userId,
        apparelId,
        enrichedMetaData,
        source,
        sourceId
      );

      console.log(
        `[apparelService][createApparel] Successfully created apparel with ID: ${apparelId}`
      );
      return wardrobeItem;
    } catch (error) {
      // Check if this is a duplicate apparel error
      if (error instanceof Error && error.message.includes("already exists for user")) {
        console.warn(
          `[apparelService][createApparel] Duplicate apparel: ${error.message}`
        );
        throw new Error(`Apparel already exists. Use updateApparel to modify existing apparels.`);
      }

      console.error(
        `[apparelService][createApparel] Unexpected error: ${error}`
      );
      throw error;
    }
  }

  async updateApparel(
    source: ApparelSource,
    apparelId: string,
    userId: string,
    apparelMetaData: Partial<ApparelMetaData>
  ): Promise<Apparel> {
    try {
      console.log(
        `[apparelService][updateApparel] Updating apparel ID: ${apparelId} for source: ${source}`
      );

      // Get the existing wardrobe item
      const existingItem = await this.userWardrobeDao.getWardrobeItem(
        userId,
        apparelId
      );
      if (!existingItem) {
        throw new Error(`Apparel not found with ID: ${apparelId}`);
      }

      // Create wardrobe updates with the new apparel metadata
      const wardrobeUpdates: UpdateWardrobeInput = {
        apparel: {
          ...existingItem.apparel,
          ...apparelMetaData,
          updatedDate: new Date().toISOString(),
          updatedBy: source,
          // Preserve the original type information and apparelId
          apparelProfile: existingItem.apparel.apparelProfile,
          apparelType: existingItem.apparel.apparelType,
          apparelCategory: existingItem.apparel.apparelCategory,
          apparelId: apparelId // Ensure apparelId is preserved
        } as Apparel
      };

      // Update the wardrobe item
      const updatedItem = await this.userWardrobeDao.updateWardrobeItem(
        userId,
        apparelId,
        wardrobeUpdates
      );

      console.log(
        `[apparelService][updateApparel] Successfully updated apparel ID: ${apparelId}`
      );
      return updatedItem.apparel;
    } catch (error) {
      console.error(
        `[apparelService][updateApparel] Unexpected error: ${error}`
      );
      throw error;
    }
  }

  async getApparel(userId: string, apparelId: string): Promise<Apparel | null> {
    try {
      console.log(
        `[apparelService][getApparel] Getting apparel ID: ${apparelId}`
      );
      const wardrobeItem = await this.userWardrobeDao.getWardrobeItem(userId, apparelId);
      console.log(
        `[apparelService][getApparel] ${
          wardrobeItem ? "Successfully retrieved" : "Could not find"
        } apparel ID: ${apparelId}`
      );
      return wardrobeItem?.apparel || null;
    } catch (error) {
      console.error(`[apparelService][getApparel] Unexpected error: ${error}`);
      throw error;
    }
  }

  async listApparels(userId: string): Promise<UserWardrobeInfo[]> {
    try {
      console.log(
        `[apparelService][listApparels] Starting list apparel for userId: ${userId}`
      );

      // Source restriction removed - we can list all user apparels regardless of source

      const { items } = await this.userWardrobeDao.listWardrobeItems(userId);
      console.log(
        `[apparelService][listApparels] Successfully retrieved ${items.length} items`
      );
      return items;
    } catch (error) {
      console.error(
        `[apparelService][listApparels] Unexpected error: ${error}`
      );
      throw error;
    }
  }

  /**
   * List apparels with filters
   * @param userId The user ID
   * @param filters Optional filters to apply (apparelCategory, apparelProfile, etc.)
   * @param limit Maximum number of items to return
   * @param startKey Optional starting key for pagination
   * @returns Filtered apparels and last evaluated key for pagination
   */
  async listApparelsWithFilters(
    userId: string,
    filters?: WardrobeItemFilters,
    limit = 50,
    startKey?: any
  ): Promise<{ items: UserWardrobeInfo[]; lastKey?: any }> {
    try {
      console.log(
        `[apparelService][listApparelsWithFilters] Starting filtered list for userId: ${userId}`,
        { filters: filters ? JSON.stringify(filters) : "none" }
      );

      // Use the new DAO method to get filtered items
      const result = await this.userWardrobeDao.listWardrobeItemsWithFilters(
        userId,
        filters,
        limit,
        startKey
      );

      console.log(
        `[apparelService][listApparelsWithFilters] Successfully retrieved ${result.items.length} items`
      );

      return result;
    } catch (error) {
      console.error(
        `[apparelService][listApparelsWithFilters] Unexpected error: ${error}`
      );
      throw error;
    }
  }

  async archiveWardrobeApparel(
    userId: string,
    apparelId: string
  ): Promise<void> {
    try {
      console.log(
        `[apparelService][archiveApparel] Archiving apparel ID: ${apparelId}`
      );
      await this.userWardrobeDao.updateWardrobeItem(userId, apparelId, {
        wardrobeApparelStatus: ApparelStatus.ARCHIVED,
      });
      console.log(
        `[apparelService][archiveApparel] Successfully archived apparel ID: ${apparelId}`
      );
    } catch (error) {
      console.error(
        `[apparelService][archiveApparel] Unexpected error: ${error}`
      );
      throw error;
    }
  }

  async updateWardrobeApparel(
    userId: string,
    apparelId: string,
    wardrobeUpdates: UpdateWardrobeInput
  ): Promise<void> {
    try {
      console.log(
        `[apparelService][updateWardrobeApparel] Updating apparel ID: ${apparelId}`
      );
      await this.userWardrobeDao.updateWardrobeItem(
        userId,
        apparelId,
        wardrobeUpdates
      );
      console.log(
        `[apparelService][updateWardrobeApparel] Successfully updated apparel ID: ${apparelId}`
      );
    } catch (error) {
      console.error(
        `[apparelService][updateWardrobeApparel] Unexpected error: ${error}`
      );
      throw error;
    }
  }

  private generateApparelId(
    sourceId: string,
    source: ApparelSource,
  ): string {
    if (source === ApparelSource.MYNTRA) {
      return `${sourceId}_${source}`;
    }

    if (source === ApparelSource.USER) {
      const uuid = uuidv4();
      return `${sourceId}_${source}_${uuid}`;
    }

    throw new Error(`Unsupported apparel source: ${source}`);
  }

  /**
   * Create multiple apparels from images
   * This method processes each image in sequence, adding successfully created apparels
   * to the existingApparels array to prevent duplicates across images in the same batch.
   *
   * @param request The request containing userId, imageUrls, and gender
   * @returns Response with success status, created apparels, and any duplicates
   */
  async createApparelsFromImages(
    request: CreateApparelsFromImagesRequest
  ): Promise<CreateApparelsFromImagesResponse> {
    try {
      const { userId, imageUrls, gender } = request;

      console.log(`[apparelService][createApparelsFromImages] Starting apparel creation for user ${userId}`, {
        imageCount: imageUrls.length,
        gender
      });

      // Get existing apparels once to use for duplicate checking
      // Using a mutable array so we can add newly created apparels during processing
      const existingApparels = await this.listApparels(userId);
      console.log(`[apparelService][createApparelsFromImages] Found ${existingApparels.length} existing apparels for user ${userId}`);

      // Track processing results
      const results = {
        success: true,
        message: 'Processing complete',
        apparels: [] as UserWardrobeInfo[],
        duplicates: [] as { imageUrl: string; apparelDescriptions: string[] }[],
        processedImages: imageUrls.length,
        successfullyProcessed: 0,
        timestamp: new Date().toISOString()
      };

      // Process each image with the optimized approach
      for (const imageUrl of imageUrls) {
        try {
          console.log(`[apparelService][createApparelsFromImages] Processing image ${imageUrls.indexOf(imageUrl) + 1}/${imageUrls.length}`);

          // Step 1: Combined validation, duplicate detection, and tagging in a single call
          const processResult = await this.apparelProcessingUtil.processApparelImage(
            imageUrl,
            existingApparels,
            gender
          );

          // If the image is invalid, add to duplicates and continue to next image
          if (!processResult.isValid) {
            console.log(`[apparelService][createApparelsFromImages] Image validation failed: ${processResult.errorMessage}`);
            results.duplicates.push({
              imageUrl,
              apparelDescriptions: [processResult.errorMessage || 'Image validation failed']
            });
            continue; // Skip to the next image
          }

          // If we have duplicate descriptions, add them to the results
          if (processResult.duplicateDescriptions.length > 0) {
            console.log(`[apparelService][createApparelsFromImages] Found ${processResult.duplicateDescriptions.length} duplicates`);
            results.duplicates.push({
              imageUrl,
              apparelDescriptions: processResult.duplicateDescriptions
            });
          }

          // If we have no unique apparels, continue to next image
          if (processResult.uniqueApparels.length === 0) {
            console.log(`[apparelService][createApparelsFromImages] No unique apparels found`);
            continue;
          }

          console.log(`[apparelService][createApparelsFromImages] Found ${processResult.uniqueApparels.length} unique apparels`);

          // Process each unique apparel
          for (const apparelData of processResult.uniqueApparels) {
            // Step 2: Generate a virtual try-off image (not try-on)
            console.log(`[apparelService][createApparelsFromImages] Generating virtual try-off image for ${apparelData.apparelType}`);

            // Create a properly typed ApparelMetaData object using a more efficient approach
            // Start with the required base fields
            const baseMetadata = {
              apparelCategory: apparelData.apparelCategory,
              apparelProfile: gender,
              apparelType: apparelData.apparelType,
              productName: apparelData.productName || `${apparelData.colour || ''} ${apparelData.apparelType}`,
              apparelMediaUrl: apparelData.apparelMediaUrl || imageUrl,
              apparelSourceMediaUrl: apparelData.apparelSourceMediaUrl || imageUrl,
            };

            // Create a clean object with only the properties that exist in the apparelData
            // This avoids adding undefined values and keeps the object clean
            const additionalProps = Object.entries(apparelData)
              .filter(([key, value]) =>
                value !== undefined &&
                !Object.keys(baseMetadata).includes(key)
              )
              .reduce((obj, [key, value]) => ({ ...obj, [key]: value }), {});

            // Combine the base metadata with additional properties
            // TypeScript will enforce the correct types when we cast to ApparelMetaData
            const apparelMetadata = {
              ...baseMetadata,
              ...additionalProps
            } as ApparelMetaData;

            try {
              const virtualTryOffUrl = await this.generateVirtualTryOff(imageUrl, apparelMetadata, userId);

              // Set the virtual try-off image as the main apparel media URL
              apparelMetadata.apparelMediaUrl = virtualTryOffUrl;

              // Step 3: Save the apparel using the apparelService
              console.log(`[apparelService][createApparelsFromImages] Saving apparel to wardrobe`);
              const createdApparel = await this.createApparel(
                ApparelSource.USER,
                apparelMetadata,
                userId,
                userId // Using userId as sourceId
              );
              console.log(`[apparelService][createApparelsFromImages] Successfully saved apparel with ID: ${createdApparel.apparel.apparelId}`);

              // Add to successful results
              results.apparels.push(createdApparel);
              results.successfullyProcessed++;

              // Add the newly created apparel to existingApparels to prevent duplicates in subsequent images
              existingApparels.push(createdApparel);
            } catch (vtoffError) {
              // Log the VTOFF error and add to duplicates
              console.error(`[apparelService][createApparelsFromImages] Error in Virtual Try-OFF generation for ${apparelMetadata.apparelType}:`, {
                error: vtoffError instanceof Error ? vtoffError.message : 'Unknown error'
              });

              // Add to duplicates with specific error message
              results.duplicates.push({
                imageUrl,
                apparelDescriptions: [`Failed to generate Virtual Try-OFF image for ${apparelMetadata.apparelType}: ${vtoffError instanceof Error ? vtoffError.message : 'Unknown error'}`]
              });

              // Skip to the next apparel
              continue;
            }
          }
        } catch (imageError) {
          // Log error but continue processing other images
          console.error(`[apparelService][createApparelsFromImages] Error processing image:`, {
            error: imageError instanceof Error ? imageError.message : 'Unknown error'
          });

          results.duplicates.push({
            imageUrl,
            apparelDescriptions: [imageError instanceof Error ? imageError.message : 'Error processing image']
          });
        }
      }

      return results;
    } catch (error) {
      console.error(`[apparelService][createApparelsFromImages] Error creating apparels:`, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create apparels',
        apparels: [],
        duplicates: [],
        processedImages: 0,
        successfullyProcessed: 0,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Generate a virtual try-OFF image (catalog-style image of the apparel)
   * @param imageUrl The URL of the source image
   * @param metadata The apparel metadata
   * @param userId The user ID
   * @returns The URL of the generated image
   */
  private async generateVirtualTryOff(
    imageUrl: string,
    metadata: ApparelMetaData,
    userId: string
  ): Promise<string> {
    try {
      // Create a descriptive product name for better image generation
      const productDescription = metadata.productName ||
        `${metadata.colour || ''} ${metadata.pattern || ''} ${metadata.fabric || ''} ${metadata.apparelType}`.trim();

      console.log(`[apparelService][generateVirtualTryOff] Generating virtual try-OFF image for ${metadata.apparelType}`);

      const generatedImageResult = await this.aiService.generateImageUsingGeminiMultiModal(
        imageUrl,
        APPAREL_IMAGE_GENERATION_USER_PROMPT(productDescription),
        APPAREL_IMAGE_GENERATION_SYSTEM_PROMPT
      );

      if (!generatedImageResult.imageData) {
        console.warn(`[apparelService][generateVirtualTryOff] No image data generated`);
        throw new Error('Virtual Try-OFF image generation failed: No image data returned');
      }

      console.log(`[apparelService][generateVirtualTryOff] Successfully received image data`);

      const filename = `${metadata.apparelCategory.toLowerCase()}-${metadata.apparelType}-${Date.now()}`;

      // Create a temporary file path
      const tempFilePath = `/tmp/virtual-tryoff-${filename}.jpg`;
      require('fs').writeFileSync(tempFilePath, generatedImageResult.imageData);

      // Upload to storage with organized folder structure
      const storagePath = `users/${userId}/catalog-images/${filename}`;
      console.log(`[apparelService][generateVirtualTryOff] Uploading image to Azure for user ${userId}`);

      const storedImageUrl = await this.imageStorageService.uploadImage(
        tempFilePath,
        storagePath
      );

      console.log(`[apparelService][generateVirtualTryOff] Successfully uploaded image to Azure`);

      // Clean up the temporary file
      require('fs').unlinkSync(tempFilePath);

      return storedImageUrl;
    } catch (error) {
      console.error(`[apparelService][generateVirtualTryOff] Error generating virtual try-OFF:`, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      // Throw the error to be handled by the caller
      throw new Error(`Virtual Try-OFF image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
