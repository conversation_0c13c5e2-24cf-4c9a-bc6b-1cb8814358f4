import { OutfitCurationUtil } from "../utils/outfitCurationUtil";
import { OptimizedOutfitCurationUtil } from "../utils/optimizedOutfitCurationUtil";
import { UserWardrobeDao } from "../db/userWardrobeInfoDao";
import { OutfitCollectionDao } from "../db/outfitCollectionDao";
import { ApparelService } from "./apparelService";
import {
  Apparel,
  ApparelSource,
  ApparelStatus,
  ApparelVibe } from "../types/apparel.types";
import { OutfitDao } from "../db/outfitDao";
import {
  CreateOutfitInput,
  OutfitStore,
  UpdateOutfitInput,
  OutfitStatus,
  OutfitError,
  WeeklyOutfitCollection,
  CurateOutfitsParams,
  SituationalContext } from "../types/outfits.types";

export class OutfitService {
  private outfitDao: OutfitDao;
  private userWardrobeDao: UserWardrobeDao;
  private outfitCollectionDao: OutfitCollectionDao;
  private apparelService: ApparelService;
  private outfitCurationUtil: OutfitCurationUtil;

  constructor() {
    this.outfitDao = new OutfitDao();
    this.userWardrobeDao = UserWardrobeDao.getInstance();
    this.outfitCollectionDao = new OutfitCollectionDao();
    this.apparelService = new ApparelService();

    // Create OutfitCurationUtil and set this service instance
    this.outfitCurationUtil = new OutfitCurationUtil();
    this.outfitCurationUtil.setOutfitService(this);
  }

  async createOutfit(input: CreateOutfitInput): Promise<OutfitStore> {
    try {
      console.log("[OutfitService][createOutfit] Creating outfit:", input);

      // Create the outfit
      const outfit = await this.outfitDao.createOutfit(input);

      // Update outfitCombinations for each apparel
      await this.updateApparelOutfitReferences(
        input.userId,
        outfit.outfitId,
        input.apparelPointers
      );

      // If this outfit belongs to a collection, add it to the collection
      if (input.outfitCollectionId) {
        await this.addOutfitToCollection(
          input.userId,
          input.outfitCollectionId,
          outfit.outfitId
        );
      }

      return outfit;
    } catch (error) {
      console.error("[OutfitService][createOutfit] Error:", error);
      throw error;
    }
  }

  async getOutfit(
    userId: string,
    outfitId: string
  ): Promise<OutfitStore | null> {
    return await this.outfitDao.getOutfit(userId, outfitId);
  }

  async listOutfits(userId: string): Promise<OutfitStore[]> {
    const { items } = await this.outfitDao.listOutfits(userId);
    return items;
  }

  async updateOutfit(
    userId: string,
    outfitId: string,
    updates: UpdateOutfitInput
  ): Promise<OutfitStore> {
    try {
      if (
        updates.status === OutfitStatus.WISHLIST &&
        updates.outfitCollectionId
      ) {
        await this.addOutfitToCollection(
          userId,
          updates.outfitCollectionId,
          outfitId
        );
      }
      // Update the outfit
      const updatedOutfit = await this.outfitDao.updateOutfit(
        userId,
        outfitId,
        updates
      );
      return updatedOutfit;
    } catch (error) {
      console.error("[OutfitService][updateOutfit] Error:", error);
      throw error;
    }
  }

  async archiveOutfit(
    userId: string,
    outfitId: string,
    outfitCollectionId: string
  ): Promise<void> {
    try {
      // Get the outfit to find its apparels
      if (outfitCollectionId) {
        await this.removeOutfitFromCollection(
          userId,
          outfitCollectionId,
          outfitId
        );
        const updatedOutfit = await this.outfitDao.updateOutfit(
          userId,
          outfitId,
          {
            status: OutfitStatus.ARCHIVED,
            outfitCollectionId: null,
          }
        );
      }
    } catch (error) {
      console.error("[OutfitService][deleteOutfit] Error:", error);
      throw error;
    }
  }

  async curateOutfits({
    userId,
    time,
    location,
    weather,
  }: CurateOutfitsParams): Promise<OutfitStore[]> {
    if (!userId) {
      throw new Error("User ID is required for outfit curation");
    }

    try {
      // 1. Fetch user's wardrobe
      const userWardrobe = await this.apparelService.listApparels(
        userId
      );
      if (!userWardrobe) {
        return [];
      }

      // Filter out archived apparels
      const activeWardrobe = userWardrobe.filter(
        (item) => item.wardrobeApparelStatus !== ApparelStatus.ARCHIVED
      );

      // 2. Get apparel details directly from wardrobe items
      const apparels = activeWardrobe.map(item => item.apparel);

      // 3. Validate minimum apparel requirements
      console.log(apparels);
      this.outfitCurationUtil.validateEligibilityForOutfitCuration(apparels);

      // Collect vibes from apparels
      const vibes = new Set<string>();
      apparels.forEach((apparel) => {
        if (apparel?.vibe) {
          vibes.add(apparel.vibe);
        }
      });

      // If no vibes found, use random vibes from available types
      if (vibes.size === 0) {
        console.warn(
          "[OutfitService] No vibes found in wardrobe, using random vibes"
        );
        const allVibes: ApparelVibe[] = [
          "bold",
          "playful",
          "casual",
          "edgy",
          "minimal",
          "ethnic",
          "bohemian",
          "sporty",
          "elegant",
          "professional",
        ];

        // Randomly select 3 vibes instead of 6
        while (vibes.size < 3) {
          const randomIndex = Math.floor(Math.random() * allVibes.length);
          vibes.add(allVibes[randomIndex]);
        }
      }

      // 4. Generate situational contexts
      const contexts: SituationalContext[] = this.generateSituationalContexts(
        vibes,
        time,
        location,
        weather
      );

      const outfits = await this.outfitCurationUtil.processOutfitCurationFromExistingApparels(
        userId,
        contexts,
        apparels
      );

      return outfits;
    } catch (error) {
      console.error("Error curating outfits:", error);
      throw error;
    }
  }

  private generateSituationalContexts(
    vibes: Set<string>,
    time?: string,
    location?: string,
    weather?: string
  ): SituationalContext[] {
    // Default values if parameters are not provided
    const defaultTimes = ["MORNING", "AFTERNOON", "EVENING"];
    const defaultLocations = ["INDOOR", "OUTDOOR", "OFFICE"]; //We can add this to the userStyleProfile as default location types
    const defaultWeathers = ["SUNNY", "RAINY", "COLD", "PLEASANT"]; //This needs to be fetched based on userLocation, date and weather APIs

    const vibesArray = Array.from(vibes);
    const contexts: SituationalContext[] = [];

    // Generate 6 contexts
    for (let i = 0; i < 3; i++) {
      const context: SituationalContext = {
        // Use modulo to repeat vibes if less than 6 available
        eventOutfitVibe: vibesArray[i % vibesArray.length],
        // Use provided values or cycle through defaults
        eventTime: time || defaultTimes[i % defaultTimes.length],
        eventLocation:
          location || defaultLocations[i % defaultLocations.length],
        eventWeather: weather || defaultWeathers[i % defaultWeathers.length],
      };
      contexts.push(context);
    }

    // Log the distribution of contexts
    console.log("[OutfitService] Generated contexts:", {
      totalContexts: contexts.length,
      uniqueVibes: vibesArray.length,
      contexts,
    });

    return contexts;
  }

  // Add to outfitService.ts

  // Add this new method to the OutfitService class
  async getOptimizedOutfit(
    userId: string,
    context: SituationalContext
  ): Promise<OutfitStore> {
    if (!userId) {
      throw new Error("User ID is required for outfit curation");
    }

    try {
      console.log(
        "[OutfitService][getOptimizedOutfit] Creating optimized outfit:",
        {
          userId,
          context,
        }
      );

      // 1. Fetch user's wardrobe
      const userWardrobe = await this.apparelService.listApparels(
        userId
      );
      if (!userWardrobe || userWardrobe.length === 0) {
        throw new OutfitError(
          "No apparels found in user's wardrobe",
          404,
          "EMPTY_WARDROBE"
        );
      }

      // Filter out archived apparels
      const activeWardrobe = userWardrobe.filter(
        (item) => item.wardrobeApparelStatus !== ApparelStatus.ARCHIVED
      );

      // 2. Get apparel details directly from wardrobe items
      const apparels = activeWardrobe.map(item => item.apparel);

      // 3. Validate minimum apparel requirements
      console.log(apparels);
      this.outfitCurationUtil.validateEligibilityForOutfitCuration(apparels);

      // 4. Use the new optimized curation util with the single context
      const optimizedCurationUtil = new OptimizedOutfitCurationUtil();
      const outfit = await optimizedCurationUtil.createOptimizedOutfit(
        userId,
        context,
        apparels,
        this
      );

      return outfit;
    } catch (error) {
      console.error("[OutfitService][getOptimizedOutfit] Error:", error);
      throw error;
    }
  }

  async getWeeklyOutfits({
    userId,
    weather
  }: CurateOutfitsParams): Promise<WeeklyOutfitCollection> {
    console.log("[OutfitService][getWeeklyOutfits] Starting with params:", { userId, weather });

    if (!userId || !weather) {
      const missingParam = !userId ? "User ID" : "weather";
      console.error(`[OutfitService][getWeeklyOutfits] Missing required parameter: ${missingParam}`);
      throw new Error("Both User ID and weather are required for outfit curation");
    }

    try {
      // 1. Try to fetch existing weekly collection
      console.log("[OutfitService][getWeeklyOutfits] Fetching existing weekly collection for user:", userId);
      const existingCollection = await this.outfitCollectionDao.getWeeklyOutfitCollection(userId);
      console.log("[OutfitService][getWeeklyOutfits] Existing collection:", existingCollection);

      // Get today's date and the next 6 days
      const days = Array.from({length: 7}, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() + i);
        return date.toISOString().split('T')[0]; // Format: YYYY-MM-DD
      });
      console.log("[OutfitService][getWeeklyOutfits] Generated days:", days);

      // 2. Check which days need outfits
      const missingDays = days.filter(day => {
        if (!existingCollection?.outfits?.[day]) {
          console.log("[OutfitService][getWeeklyOutfits] Day missing:", day);
          return true;
        }
        // Check if there's at least one outfit for this day
        const hasOutfits = !Object.values(existingCollection.outfits[day]).every(
          outfitStore => !outfitStore || !outfitStore.apparelPointers || outfitStore.apparelPointers.length === 0
        );
        if (!hasOutfits) {
          console.log("[OutfitService][getWeeklyOutfits] Day has no valid outfits:", day);
        }
        return !hasOutfits;
      });
      console.log("[OutfitService][getWeeklyOutfits] Missing days:", missingDays);

      // If no missing days and collection exists, return existing collection
      if (missingDays.length === 0 && existingCollection) {
        console.log("[OutfitService][getWeeklyOutfits] No missing days, returning existing collection");
        return existingCollection;
      }

      // 3. Fetch and validate wardrobe
      console.log("[OutfitService][getWeeklyOutfits] Fetching user wardrobe");
      const userWardrobe = await this.apparelService.listApparels(
        userId
      );
      if (!userWardrobe) {
        console.error("[OutfitService][getWeeklyOutfits] No apparels found for user");
        throw new OutfitError("No apparels found", 404, "EMPTY_WARDROBE");
      }

      // Filter out archived apparels
      const activeWardrobe = userWardrobe.filter(
        (item) => item.wardrobeApparelStatus !== ApparelStatus.ARCHIVED
      );

      // 2. Get apparel details directly from wardrobe items
      const apparels = activeWardrobe.map(item => item.apparel);

      // Validate minimum apparel requirements
      this.outfitCurationUtil.validateEligibilityForOutfitCuration(apparels);

      // Collect vibes from apparels
      const vibes = new Set<string>();
      apparels.forEach((apparel) => {
        if (apparel?.vibe) {
          vibes.add(apparel.vibe);
        }
      });

      // // If no vibes found, use default vibes
      // if (vibes.size === 0) {
      //   const allVibes: ApparelVibe[] = [
      //     "bold", "playful", "casual", "edgy", "minimal",
      //     "ethnic", "bohemian", "sporty", "elegant", "professional"
      //   ];
      //   while (vibes.size < 3) {
      //     const randomIndex = Math.floor(Math.random() * allVibes.length);
      //     vibes.add(allVibes[randomIndex]);
      //   }
      // }

      // 5. Initialize or use existing weekly collection
      const weeklyCollection: WeeklyOutfitCollection = {
        userId,
        outfitCollectionId: "weekly-outfits",
        outfitCollectionName: "Weekly Outfits",
        outfits: existingCollection?.outfits || {},
        dateCreated: existingCollection?.dateCreated || new Date().toISOString()
      };
      console.log("[OutfitService][getWeeklyOutfits] Initialized weekly collection");

      // 6. Generate outfits for missing days
      for (const day of missingDays) {
        console.log("[OutfitService][getWeeklyOutfits] Generating outfit for day:", day);
        // Pick a random vibe for this day
        const vibesArray = Array.from(vibes);
        const randomVibe = vibesArray[Math.floor(Math.random() * vibesArray.length)];
        console.log("[OutfitService][getWeeklyOutfits] Selected vibe:", randomVibe);

        const defaultTimes = ["MORNING", "AFTERNOON", "EVENING", "NIGHT"];
        const defaultLocations = ["INDOOR", "OUTDOOR"]; //We can add this to the userStyleProfile as default location types
        // Create context for this day
        const context: SituationalContext = {
          eventOutfitVibe: randomVibe,
          eventTime: defaultTimes[Math.floor(Math.random() * defaultTimes.length)],
          eventLocation: defaultLocations[Math.floor(Math.random() * defaultLocations.length)],
          eventWeather: weather
        };

        // Generate outfit using processOutfitCuration with single context
        const outfits = await this.outfitCurationUtil.processOutfitCurationFromExistingApparels(
          userId,
          [context], // Send array with single context
          apparels
        );
        console.log("[OutfitService][getWeeklyOutfits] Generated outfits count:", outfits.length);

        // Add the outfit to the collection for this day if generation was successful
        if (outfits.length > 0) {
          if (!weeklyCollection.outfits[day]) {
            weeklyCollection.outfits[day] = {};
          }
          weeklyCollection.outfits[day][randomVibe] = outfits[0];
          console.log("[OutfitService][getWeeklyOutfits] Added outfit to collection for day:", day);
        } else {
          console.warn("[OutfitService][getWeeklyOutfits] No outfits generated for day:", day);
        }
      }

      // 7. Save or update collection
      console.log("[OutfitService][getWeeklyOutfits] Saving/updating collection");
      if (!existingCollection) {
        console.log("[OutfitService][getWeeklyOutfits] Creating new collection");
        await this.outfitCollectionDao.createWeeklyOutfitCollection(weeklyCollection);
      } else {
        console.log("[OutfitService][getWeeklyOutfits] Updating existing collection");
        await this.outfitCollectionDao.updateWeeklyOutfitCollection(
          userId,
          { outfits: weeklyCollection.outfits }
        );
      }

      console.log("[OutfitService][getWeeklyOutfits] Successfully completed");
      return weeklyCollection;
    } catch (error) {
      console.error("[OutfitService][getWeeklyOutfits] Error:", error);
      throw error;
    }
  }

  async getTodayOutfits(userId: string, weather: string): Promise<{ [vibe: string]: OutfitStore }> {
    if (!userId || !weather) {
      const missingParam = !userId ? "User ID" : "weather";
      console.error(`[OutfitService] Missing required parameter: ${missingParam}`);
      throw new OutfitError("Both User ID and weather are required for getting today's outfits");
    }

    try {
      // Default vibes to use when user's wardrobe doesn't have enough vibes
      const defaultVibes: ApparelVibe[] = [
        "casual", "professional", "elegant", "sporty", "minimal",
        "bold", "playful", "edgy", "ethnic", "bohemian"
      ];

      // Target number of outfits with different vibes we want to generate
      const targetVibeCount = 3;

      // Will hold vibes extracted from user's apparel items
      const availableVibes = new Set<string>();

      // Will hold vibes that are already used in existing outfits
      let existingVibes: string[] = [];
      let existingVibesSet: Set<string>;

      // Number of additional vibes needed to reach target count
      let neededVibesCount: number;

      // Array version of available vibes for easier manipulation
      let availableVibesArray: string[];

      // Vibes from user's apparels that don't overlap with existing outfits
      let nonOverlappingVibes: string[];

      // Pool of candidate vibes to select from when creating new outfits
      let candidateVibes: string[];

      // Final selected vibes for new outfits to be generated
      let newOutfitVibes: string[] = [];

      // Default time options for outfit contexts
      const defaultTimes = ["MORNING", "AFTERNOON", "EVENING", "NIGHT"];

      // Default location options for outfit contexts
      const defaultLocations = ["INDOOR", "OUTDOOR"];

      // 1. Get today's date in YYYY-MM-DD format
      const today = new Date().toISOString().split('T')[0];
      console.log("[OutfitService][getTodayOutfits] Today's date:", today);

      // 2. Try to fetch existing weekly collection
      console.log("[OutfitService][getTodayOutfits] Fetching existing weekly collection");
      const existingCollection = await this.outfitCollectionDao.getWeeklyOutfitCollection(userId);
      const todayOutfits = existingCollection?.outfits?.[today] || {};
      console.log("[OutfitService][getTodayOutfits] Existing today's outfits:", todayOutfits);

      // Count existing vibes for today
      existingVibes = Object.keys(todayOutfits);
      console.log("[OutfitService][getTodayOutfits] Existing vibes:", existingVibes);
      console.log("[OutfitService][getTodayOutfits] Target vibe count:", targetVibeCount);

      // If we already have enough outfits for today, return them
      if (existingVibes.length >= targetVibeCount) {
        console.log("[OutfitService][getTodayOutfits] Already have enough outfits, returning existing");
        return todayOutfits;
      }

      // 3. Fetch and validate wardrobe
      console.log("[OutfitService][getTodayOutfits] Fetching user wardrobe");
      const userWardrobe = await this.apparelService.listApparels(
        userId
      );
      if (!userWardrobe) {
        console.error("[OutfitService][getTodayOutfits] No apparels found");
        throw new OutfitError("No apparels found", 404, "EMPTY_WARDROBE");
      }

      // Filter out archived apparels
      const activeWardrobe = userWardrobe.filter(
        (item) => item.wardrobeApparelStatus !== ApparelStatus.ARCHIVED
      );
      console.log("[OutfitService][getTodayOutfits] Active wardrobe items:", activeWardrobe.length);

      // 2. Get apparel details directly from wardrobe items
      const apparels = activeWardrobe.map(item => item.apparel);

      // Validate minimum apparel requirements
      this.outfitCurationUtil.validateEligibilityForOutfitCuration(apparels);

      // Collect available vibes from apparels
      apparels.forEach((apparel) => {
        if (apparel?.vibe) {
          availableVibes.add(apparel.vibe);
        }
      });
      console.log("[OutfitService][getTodayOutfits] Available vibes:", Array.from(availableVibes));

      // Identify existing vibes from today's outfits
      existingVibesSet = new Set(existingVibes);
      console.log("[OutfitService][getTodayOutfits] Existing vibes set:", Array.from(existingVibesSet));

      // Calculate how many additional vibes we need
      neededVibesCount = targetVibeCount - existingVibesSet.size;
      console.log("[OutfitService][getTodayOutfits] Needed vibes count:", neededVibesCount);

      // Create a pool of vibes that don't overlap with existing vibes
      availableVibesArray = Array.from(availableVibes);
      nonOverlappingVibes = availableVibesArray.filter(vibe => !existingVibesSet.has(vibe));
      console.log("[OutfitService][getTodayOutfits] Non-overlapping vibes from apparels:", nonOverlappingVibes);

      // Start with non-overlapping vibes from apparels
      candidateVibes = [...nonOverlappingVibes];

      // If we don't have enough non-overlapping vibes, add from default vibes
      if (candidateVibes.length < neededVibesCount) {
        const nonOverlappingDefaultVibes = defaultVibes.filter(vibe =>
          !existingVibesSet.has(vibe) && !nonOverlappingVibes.includes(vibe)
        );
        candidateVibes = [...candidateVibes, ...nonOverlappingDefaultVibes];
        console.log("[OutfitService][getTodayOutfits] Added default vibes to pool:", nonOverlappingDefaultVibes);
      }
      console.log("[OutfitService][getTodayOutfits] Final candidate vibes pool:", candidateVibes);

      // Select vibes for new outfits
      for (let i = 0; i < neededVibesCount && candidateVibes.length > 0; i++) {
        const randomIndex = Math.floor(Math.random() * candidateVibes.length);
        newOutfitVibes.push(candidateVibes[randomIndex]);
        // Remove the selected vibe from the pool to avoid duplicates
        candidateVibes.splice(randomIndex, 1);
      }
      console.log("[OutfitService][getTodayOutfits] Selected new vibes:", newOutfitVibes);

      // Generate contexts for the new vibes
      const contexts: SituationalContext[] = newOutfitVibes.map((vibe) => ({
        eventOutfitVibe: vibe,
        eventTime: defaultTimes[Math.floor(Math.random() * defaultTimes.length)],
        eventLocation: defaultLocations[Math.floor(Math.random() * defaultLocations.length)],
        eventWeather: weather
      }));

      console.log("[OutfitService][getTodayOutfits] Generating outfits for contexts:", contexts.length);

      const generatedOutfitArrays = await this.outfitCurationUtil.processOutfitCurationFromExistingApparels(
        userId,
        contexts,
        apparels
      );

      console.log("[OutfitService][getTodayOutfits] Generated outfits count:", generatedOutfitArrays.length);

      // Process generated outfits and add them to todayOutfits
      generatedOutfitArrays.forEach((outfit) => {
        if (outfit && outfit.situationContext?.eventOutfitVibe) {
          const vibe = outfit.situationContext.eventOutfitVibe;
          todayOutfits[vibe] = outfit;
          console.log(`[OutfitService][getTodayOutfits] Added outfit for vibe: ${vibe}`);
        } else {
          console.warn("[OutfitService][getTodayOutfits] Skipped outfit due to missing vibe in situationContext");
        }
      });

      // Update the weekly collection
      console.log("[OutfitService][getTodayOutfits] Updating weekly collection");
      if (existingCollection) {
        console.log("[OutfitService][getTodayOutfits] Updating existing collection");
        await this.outfitCollectionDao.updateWeeklyOutfitCollection(
          userId,
          {
            outfits: {
              ...existingCollection.outfits,
              [today]: todayOutfits
            }
          }
        );
      } else {
        console.log("[OutfitService][getTodayOutfits] Creating new collection");
        const weeklyCollection: WeeklyOutfitCollection = {
          userId,
          outfitCollectionId: "weekly-outfits",
          outfitCollectionName: "Weekly Outfits",
          outfits: { [today]: todayOutfits },
          dateCreated: new Date().toISOString()
        };
        await this.outfitCollectionDao.createWeeklyOutfitCollection(weeklyCollection);
      }

      console.log("[OutfitService][getTodayOutfits] Successfully completed");
      return todayOutfits;
    } catch (error) {
      console.error("[OutfitService][getTodayOutfits] Error:", error);
      throw error;
    }
  }

  async getDayVibeOutfit(
    userId: string,
    day: string,
    vibe: string,
    weather: string
  ): Promise<WeeklyOutfitCollection> {
    console.log("[OutfitService][getDayVibeOutfit] Starting with params:", { userId, day, vibe, weather });

    if (!userId || !day || !vibe || !weather) {
      const missingParams = [];
      if (!userId) missingParams.push("userId");
      if (!day) missingParams.push("day");
      if (!vibe) missingParams.push("vibe");
      if (!weather) missingParams.push("weather");
      console.error("[OutfitService][getDayVibeOutfit] Missing parameters:", missingParams);
      throw new Error(`Missing required parameters: ${missingParams.join(", ")}`);
    }

    try {
      console.log("[OutfitService][getDayVibeOutfit] Getting outfit for:", {
        userId,
        day,
        vibe,
        weather
      });

      // 1. Get the weekly collection
      const existingCollection = await this.outfitCollectionDao.getWeeklyOutfitCollection(userId);

      // 2. Check if we already have an outfit for this day and vibe
      const existingOutfit = existingCollection?.outfits?.[day]?.[vibe];
      if (existingOutfit) {
        console.log("[OutfitService][getDayVibeOutfit] Found existing outfit");
        return existingCollection;
      }

      // 3. If no existing outfit, generate a new one
      // First fetch and validate wardrobe
      const userWardrobe = await this.apparelService.listApparels(
        userId
      );
      if (!userWardrobe) {
        throw new OutfitError("No apparels found", 404, "EMPTY_WARDROBE");
      }

      // Filter out archived apparels
      const activeWardrobe = userWardrobe.filter(
        (item) => item.wardrobeApparelStatus !== ApparelStatus.ARCHIVED
      );

      // 2. Get apparel details directly from wardrobe items
      const apparels = activeWardrobe.map(item => item.apparel);

      // Validate minimum apparel requirements
      this.outfitCurationUtil.validateEligibilityForOutfitCuration(apparels);

      // 4. Create context for the new outfit
      const defaultTimes = ["MORNING", "AFTERNOON", "EVENING", "NIGHT"];
      const defaultLocations = ["INDOOR", "OUTDOOR"];
      const context: SituationalContext = {
        eventOutfitVibe: vibe,
        eventTime: defaultTimes[Math.floor(Math.random() * defaultTimes.length)],
        eventLocation: defaultLocations[Math.floor(Math.random() * defaultLocations.length)],
        eventWeather: weather
      };

      // 5. Generate new outfit
      const outfits = await this.outfitCurationUtil.processOutfitCurationFromExistingApparels(
        userId,
        [context],
        apparels
      );

      if (outfits.length === 0) {
        throw new OutfitError(
          "Failed to generate outfit for the specified vibe",
          500,
          "OUTFIT_GENERATION_FAILED"
        );
      }

      // 6. Update the weekly collection
      const updatedCollection: WeeklyOutfitCollection = {
        userId,
        outfitCollectionId: "weekly-outfits",
        outfitCollectionName: "Weekly Outfits",
        outfits: existingCollection?.outfits || {},
        dateCreated: existingCollection?.dateCreated || new Date().toISOString()
      };

      // Initialize the day object if it doesn't exist
      if (!updatedCollection.outfits[day]) {
        updatedCollection.outfits[day] = {};
      }

      // Add the new outfit
      updatedCollection.outfits[day][vibe] = outfits[0];

      // 7. Save the updated collection
      if (existingCollection) {
        await this.outfitCollectionDao.updateWeeklyOutfitCollection(
          userId,
          { outfits: updatedCollection.outfits }
        );
      } else {
        await this.outfitCollectionDao.createWeeklyOutfitCollection(updatedCollection);
      }

      return updatedCollection;
    } catch (error) {
      console.error("[OutfitService][getDayVibeOutfit] Error:", error);
      throw error;
    }
  }

  private async updateApparelOutfitReferences(
    userId: string,
    outfitId: string,
    apparelIds: string[]
  ): Promise<void> {
    try {
      const updatePromises = apparelIds.map(async (apparelId) => {
        const wardrobeItem = await this.userWardrobeDao.getWardrobeItem(
          userId,
          apparelId
        );
        if (wardrobeItem) {
          const outfitCombinations = new Set([
            ...(wardrobeItem.outfitCombinations || []),
            outfitId,
          ]);
          await this.userWardrobeDao.updateWardrobeItem(userId, apparelId, {
            outfitCombinations: Array.from(outfitCombinations),
          });
        }
      });

      await Promise.all(updatePromises);
    } catch (error) {
      console.error(
        "[OutfitService][updateApparelOutfitReferences] Error:",
        error
      );
      throw error;
    }
  }
  // Add new methods to handle collection interactions
  private async addOutfitToCollection(
    userId: string,
    collectionId: string,
    outfitId: string
  ): Promise<void> {
    try {
      console.log(
        "[OutfitService][addOutfitToCollection] Adding outfit to collection:",
        {
          userId,
          collectionId,
          outfitId,
        }
      );

      // Get current collection
      const collection = await this.outfitCollectionDao.getOutfitCollection(
        userId,
        collectionId
      );

      if (!collection) {
        console.warn(`Collection not found: ${collectionId}, skipping update`);
        return;
      }

      // Add outfit to collection if not already present
      if (!collection.outfitPointers.includes(outfitId)) {
        const updatedPointers = [...collection.outfitPointers, outfitId];

        await this.outfitCollectionDao.updateOutfitCollection(
          userId,
          collectionId,
          { outfitPointers: updatedPointers }
        );

        console.log(
          "[OutfitService][addOutfitToCollection] Outfit added to collection successfully"
        );
      }
    } catch (error) {
      console.error("[OutfitService][addOutfitToCollection] Error:", error);
      throw error;
    }
  }

  private async removeOutfitFromCollection(
    userId: string,
    collectionId: string,
    outfitId: string
  ): Promise<void> {
    try {
      console.log(
        "[OutfitService][removeOutfitFromCollection] Removing outfit from collection:",
        {
          userId,
          collectionId,
          outfitId,
        }
      );

      // Get current collection
      const collection = await this.outfitCollectionDao.getOutfitCollection(
        userId,
        collectionId
      );

      console.log(
        `[OutfitService][removeOutfitFromCollection] Collection : `,
        collection
      );

      if (!collection) {
        console.warn(`Collection not found: ${collectionId}, skipping update`);
        return;
      }

      // Remove outfit from collection
      const updatedPointers = collection.outfitPointers.filter(
        (pointer) => pointer !== outfitId
      );

      await this.outfitCollectionDao.updateOutfitCollection(
        userId,
        collectionId,
        { outfitPointers: updatedPointers }
      );

      console.log(
        "[OutfitService][removeOutfitFromCollection] Outfit removed from collection successfully"
      );
    } catch (error) {
      console.error(
        "[OutfitService][removeOutfitFromCollection] Error:",
        error
      );
      throw error;
    }
  }
}
