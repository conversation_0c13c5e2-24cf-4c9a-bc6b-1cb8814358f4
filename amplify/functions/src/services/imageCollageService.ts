import sharp from 'sharp';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { ImageStorageService } from './imageStorageService';

export class ImageCollageService {
  private imageStorageService: ImageStorageService;

  constructor() {
    this.imageStorageService = new ImageStorageService();
    // Add debug logging
    console.log('ImageCollageService initialized');
    console.log('Sharp version:', sharp.versions);
    console.log('Sharp platform:', process.platform);
    console.log('Sharp arch:', process.arch);
  }

  /**
   * Determines the optimal layout based on the number of images
   * @param imageCount Number of images to include in the collage
   * @returns Object containing layout configuration
   */
  private determineLayout(imageCount: number): { rows: number; cols: number } {
    switch (imageCount) {
      case 0:
        return { rows: 0, cols: 0 };
      case 1:
        return { rows: 1, cols: 1 };
      case 2:
        return { rows: 1, cols: 2 };
      case 3:
        return { rows: 1, cols: 3 };
      case 4:
        return { rows: 2, cols: 2 };
      default:
        return { rows: 1, cols: imageCount };
    }
  }

  /**
   * Creates a collage from a set of image URLs or file paths and stores it in Azure Blob Storage
   * @param imageUrls Array of image URLs or file paths to include in the collage
   * @param userId User ID to associate with the collage
   * @returns URL of the stored collage image
   */
  async createAndStoreCollage(imageUrls: string[], userId: string): Promise<string> {
    try {
      console.log('Starting createAndStoreCollage');
      // Handle empty image array
      if (!imageUrls || imageUrls.length === 0) {
        throw new Error('No images provided for collage');
      }

      // If only one image, just return that image URL/path
      if (imageUrls.length === 1) {
        return imageUrls[0];
      }

      // Create a temporary directory for the collage
      const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'collage-'));
      console.log('Created temp directory:', tempDir);
      const tempFilePath = path.join(tempDir, `${userId}-collage-${Date.now()}.jpg`);
      
      // Determine the layout based on image count
      const layout = this.determineLayout(imageUrls.length);
      console.log('Layout determined:', layout);
      
      // Load all images
      console.log('Starting to load images');
      const imageBuffers = await Promise.all(
        imageUrls.map(async (urlOrPath, index) => {
          console.log(`Processing image ${index + 1}:`, urlOrPath);
          try {
            if (urlOrPath.startsWith('http')) {
              // Handle URLs
              const response = await fetch(urlOrPath);
              const buffer = Buffer.from(await response.arrayBuffer());
              return await sharp(buffer)
                .jpeg()
                .toBuffer();
            } else {
              // Handle local file paths
              return await sharp(urlOrPath)
                .jpeg()
                .toBuffer();
            }
          } catch (error) {
            console.error(`Error processing image ${index + 1}:`, error);
            throw error;
          }
        })
      );
      console.log('All images loaded successfully');
      
      // Create the collage
      console.log('Starting collage creation');
      await this.createCollage(imageBuffers, layout, tempFilePath);
      console.log('Collage created successfully');

      // Upload the collage to Azure Blob Storage
      console.log('Starting upload to Azure');
      const result = await this.imageStorageService.uploadImage(tempFilePath, `${userId}-collage`);
      console.log('Upload completed');
      
      // Clean up temporary files
      fs.rmSync(tempDir, { recursive: true, force: true });
      console.log('Cleanup completed');
      
      return result;
    } catch (error: any) {
      console.error('Error in createAndStoreCollage:', error);
      console.error('Error stack:', error.stack);
      throw new Error(`Failed to create collage: ${error}`);
    }
  }

  /**
   * Creates a collage from an array of image buffers
   * @param imageBuffers Array of image buffers
   * @param layout Layout configuration
   * @param outputPath Path to save the collage
   */
  private async createCollage(
    imageBuffers: Buffer[],
    layout: { rows: number; cols: number },
    outputPath: string
  ): Promise<void> {
    const { rows, cols } = layout;
    
    // Calculate dimensions for each image in the collage
    // For images with 3:4 aspect ratio
    const baseWidth = 300; // Base width for a single image
    const baseHeight = 400; // Base height for a single image (4/3 of width)
    
    // Calculate the total canvas dimensions based on the number of rows and columns
    const collageWidth = baseWidth * cols;
    const collageHeight = baseHeight * rows;
    
    // Calculate individual image dimensions
    const imageWidth = baseWidth;
    const imageHeight = baseHeight;
    
    // Create a blank canvas
    const canvas = sharp({
      create: {
        width: collageWidth,
        height: collageHeight,
        channels: 3,
        background: { r: 255, g: 255, b: 255 }
      }
    });
    
    // Process each image
    const composites = await Promise.all(
      imageBuffers.map(async (buffer, index) => {
        const row = Math.floor(index / cols);
        const col = index % cols;
        
        // Resize the image to fit in the collage while maintaining aspect ratio
        const resizedImage = await sharp(buffer)
          .resize(imageWidth, imageHeight, { 
            fit: 'cover', // Changed from 'contain' to 'cover' to fill the space
            background: { r: 255, g: 255, b: 255 }
          })
          .jpeg()
          .toBuffer();
        
        return {
          input: resizedImage,
          top: row * imageHeight,
          left: col * imageWidth
        };
      })
    );
    
    // Composite all images onto the canvas
    await canvas
      .composite(composites)
      .jpeg({ quality: 90 })
      .toFile(outputPath);
  }
} 