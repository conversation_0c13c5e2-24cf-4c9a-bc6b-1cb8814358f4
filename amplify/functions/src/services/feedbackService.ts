// feedbackService.ts
import { v4 as uuidv4 } from "uuid";
import { FeedbackDao } from "../db/feedbackDao";
import { CreateFeedbackRequest, Feedback } from "../types/feedback.types";

export class FeedbackService {
  private feedbackDao: FeedbackDao;

  constructor() {
    console.log("[FeedbackService] Initializing service");
    this.feedbackDao = new FeedbackDao();
  }

  async createFeedback(
    userId: string,
    feedbackRequest: CreateFeedbackRequest
  ): Promise<Feedback> {
    try {
      // Validate required fields
      if (!feedbackRequest.feedbackText || !feedbackRequest.feedbackText.trim()) {
        throw new Error("Feedback text is required");
      }

      if (!userId) {
        throw new Error("User ID is required");
      }

      console.log("[FeedbackService] Creating feedback for user:", userId);

      // Generate a unique ID for the feedback
      const feedbackId = uuidv4();

      // Prepare feedback object
      const feedbackItem: Feedback = {
        feedbackId,
        userId,
        feedbackText: feedbackRequest.feedbackText.trim(),
        ...(feedbackRequest.feedbackMediaUrl && {
          feedbackMediaUrl: feedbackRequest.feedbackMediaUrl,
        }),
        createdDate: new Date().toISOString(),
      };

      // Store feedback in database
      const result = await this.feedbackDao.createFeedback(feedbackItem);
      console.log("[FeedbackService] Feedback created successfully:", feedbackId);

      return result;
    } catch (error) {
      console.error("[FeedbackService] Error creating feedback:", error);
      throw error;
    }
  }

  async getFeedbackById(userId: string, feedbackId: string): Promise<Feedback | null> {
    try {
      if (!feedbackId) {
        throw new Error("Feedback ID is required");
      }
      
      if (!userId) {
        throw new Error("User ID is required");
      }

      console.log("[FeedbackService] Getting feedback by ID:", { userId, feedbackId });
      const feedback = await this.feedbackDao.getFeedbackById(userId, feedbackId);

      if (!feedback) {
        console.log("[FeedbackService] Feedback not found:", { userId, feedbackId });
        return null;
      }

      return feedback;
    } catch (error) {
      console.error("[FeedbackService] Error getting feedback:", error);
      throw error;
    }
  }

  async getFeedbackByUserId(userId: string): Promise<Feedback[]> {
    try {
      if (!userId) {
        throw new Error("User ID is required");
      }

      console.log("[FeedbackService] Getting feedback for user:", userId);
      const feedbackItems = await this.feedbackDao.getFeedbackByUserId(userId);
      
      console.log(
        `[FeedbackService] Found ${feedbackItems.length} feedback items for user:`,
        userId
      );

      return feedbackItems;
    } catch (error) {
      console.error("[FeedbackService] Error getting user feedback:", error);
      throw error;
    }
  }
}