import { ApparelDao } from "../db/apparelCollectionDao";
import { ApparelCategory, ApparelDocument } from "../types/apparel.types";

export class ApparelCollectionService {
  private apparelDao: ApparelDao;

  constructor() {
    console.log("[ApparelService] Initializing service");
    this.apparelDao = new ApparelDao();
  }

  async searchApparels(
    query: string,
    filters?: Partial<ApparelDocument>,
    page: number = 1,
    limit: number = 20,
    apparelProfile?: string
  ): Promise<{
    results: ApparelDocument[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    try {
      if (!query?.trim()) {
        throw new Error("Search query cannot be empty");
      }

      // Validate pagination parameters
      page = Math.max(1, page); // Ensure page is at least 1
      limit = Math.min(Math.max(1, limit), 100); // Ensure limit is between 1 and 100

      // Construct the search body
      const searchBody: any = {
        query: {
          bool: {
            must: [
              {
                query_string: {
                  query: query.toLowerCase(),
                  type: "cross_fields",
                  minimum_should_match: "70%",
                },
              },
            ],
            filter: [],
          },
        },
        sort: ["_score"],
      };

      // Add filters if provided
      if (filters && Object.keys(filters).length > 0) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== null && value !== undefined && value !== "") {
            if (typeof value === "string") {
              searchBody.query.bool.filter.push({
                term: {
                  [key]: {
                    value: value.toLowerCase(),
                    case_insensitive: true,
                  },
                },
              });
            } else if (Array.isArray(value)) {
              searchBody.query.bool.filter.push({
                terms: {
                  [key]: value.map((v) => v.toLowerCase()),
                },
              });
            }
          }
        });
      }

      // Add apparelProfile filter if provided
      if (apparelProfile) {
        searchBody.query.bool.filter.push({
          term: {
            apparelProfile: {
              value: apparelProfile.toLowerCase(),
              case_insensitive: true,
            },
          },
        });
      }

      console.log("[ApparelService] Searching apparels:", {
        query,
        filters,
        page,
        limit,
        apparelProfile,
      });

      const { results, total } = await this.apparelDao.queryInventoryForApparels(
        searchBody,
        page,
        limit
      );

      const totalPages = Math.ceil(total / limit);
      console.log(
        `[ApparelService] Found ${results.length} results (page ${page} of ${totalPages})`
      );

      return {
        results,
        total,
        page,
        pageSize: limit,
        totalPages,
      };
    } catch (error) {
      console.error("[ApparelService] Search failed:", error);
      throw error;
    }
  }

  async searchManychatApparels(
    query: string,
    page: number = 1,
    limit: number = 20,
    apparelProfile: string,
    apparelCategory: string,
    clothingType: string
  ): Promise<{
    results: ApparelDocument[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    try {
      if (!query?.trim()) {
        throw new Error("Search query cannot be empty");
      }

      if (!apparelProfile?.trim()) {
        throw new Error("Apparel profile cannot be empty");
      }

      if (!apparelCategory?.trim()) {
        throw new Error("Apparel category cannot be empty");
      }

      if (!clothingType?.trim()) {
        throw new Error("Clothing type cannot be empty");
      }

      // Validate pagination parameters
      page = Math.max(1, page); // Ensure page is at least 1
      limit = Math.min(Math.max(1, limit), 100); // Ensure limit is between 1 and 100

      // Construct the search body
      const searchBody: any = {
        size: limit,
        query: {
          bool: {
            filter: [
              {
                term: {
                  "apparelProfile.enum": {
                    value: apparelProfile.toLowerCase(),
                    case_insensitive: true
                  }
                }
              },
              {
                term: {
                  "apparelCategory.enum": {
                    value: apparelCategory.toLowerCase(),
                    case_insensitive: true
                  }
                }
              },
              {
                multi_match: {
                  query: clothingType.toLowerCase(),
                  fields: ["*"],
                  type: "best_fields"
                }
              }
            ],
            must: {
              query_string: {
                query: query.toLowerCase(),
                fields: ["*", "colour^2", "primaryHumanReadableColor^2"],
                fuzziness: "AUTO",
                type: "cross_fields",
                minimum_should_match: "70%"
              }
            }
          }
        },
        sort: ["_score"]
      };

      console.log("[ApparelService] Searching apparels:", {
        query,
        page,
        limit,
        apparelProfile,
        apparelCategory,
        clothingType
      });

      const { results, total } = await this.apparelDao.queryInventoryForApparels(
        searchBody,
        page,
        limit
      );

      const totalPages = Math.ceil(total / limit);
      console.log(
        `[ApparelService] Found ${results.length} results (page ${page} of ${totalPages})`
      );

      return {
        results,
        total,
        page,
        pageSize: limit,
        totalPages,
      };
    } catch (error) {
      console.error("[ApparelService] Search failed:", error);
      throw error;
    }
  }

  async getSuggestions(query: string, apparelProfile?: string): Promise<string[]> {
    try {
      if (!query || query.length < 4) {
        console.log("[ApparelService] Query too short for suggestions");
        return [];
      }

      console.log("[ApparelService] Getting suggestions for query:", query, "profile:", apparelProfile);
      const suggestions = await this.apparelDao.getSuggestions(query, apparelProfile) || [];
      console.log(`[ApparelService] Found ${suggestions.length} suggestions`);

      return suggestions;
    } catch (error) {
      console.error("[ApparelService] Error getting suggestions:", error);
      throw error;
    }
  }
}
