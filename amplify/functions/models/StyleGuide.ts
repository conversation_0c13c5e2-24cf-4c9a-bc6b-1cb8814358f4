// Type definitions and interfaces

import { BodyType, BodyTypeResponse, ColorSeasonResponse, FemaleBodyType, Gender, Height, HeightResponse, MaleBodyType, StyleGuideResponse } from "../src/types/style.types";
import { Season } from "../src/types/style.types";
import { ApparelCollectionService } from "../src/services/apparelCollectionService";

const apparelCollectionService = new ApparelCollectionService();

interface BaseStyleGuideData {
  season: Record<Season, ColorSeasonResponse>;
  height: Record<Height, HeightResponse>;
}

interface MasculineStyleGuideData extends BaseStyleGuideData {
  bodyType: Record<MaleBodyType, BodyTypeResponse>;
}

interface FeminineStyleGuideData extends BaseStyleGuideData {
  bodyType: Record<FemaleBodyType, BodyTypeResponse>;
}

// Main data structure
const styleGuideData: {
  MALE: MasculineStyleGuideData;
  FEMALE: FeminineStyleGuideData;
} = {
  MALE: {
    season: {
      SPRING: {
        ColorPaletteUrl:
          "https://monovaoutfits.blob.core.windows.net/color-palette/Spring_Palette.png",
        SeasonSuggestions: [
          "With your warm undertone, your your colour season is Spring. It's is all about radiance, energy, and life! Imagine sunny days, tropical waters, and blooming flowers—your colors should be just as vibrant and uplifting.",
          "Wear this: Warm pastels, juicy citrus tones, golden yellows, clear blues, and lively pinks that make you glow.",
        ].map((suggestion) =>
          suggestion
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB")
        ),
        SeasonTags: ["Warm pastels", "Cool pastels", "Warm shades"],
      },
      AUTUMN: {
        ColorPaletteUrl:
          "https://monovaoutfits.blob.core.windows.net/color-palette/Autumn_Palette.png",
        SeasonSuggestions: [
          "With a warm undertone, your colour season, Autumn, is all about depth, warmth, and richness. Think golden forests, spiced lattes, and cozy evenings by the fire—your palette is effortlessly stunning and sophisticated.",
          "Wear this: Earthy neutrals, golden undertones, deep greens, warm reds, and rich browns that bring out your natural warmth.",
        ].map((suggestion) =>
          suggestion
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB")
        ),
        SeasonTags: ["Neutrals", "Warm shades", "Cool Shades"],
      },
      WINTER: {
        ColorPaletteUrl:
          "https://monovaoutfits.blob.core.windows.net/color-palette/Winter_Palette.png",
        SeasonSuggestions: [
          "With a cool undertone, your colour season, winter, is all about sharp contrasts, rich depth, and undeniable presence. Think crisp snow, midnight skies, and striking jewel tones—your palette is designed to make an impact.",
          "Wear this: Crisp cool tones, deep jewel hues, high-contrast neutrals, icy brights, and intense blues that command attention.",
        ].map((suggestion) =>
          suggestion
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB")
        ),
        SeasonTags: ["Cool shades"],
      },
      SUMMER: {
        ColorPaletteUrl:
          "https://monovaoutfits.blob.core.windows.net/color-palette/Summer_Palette.png",
        SeasonSuggestions: [
          "You have a cool undertone, and your Summer season thrives in soft, serene, and effortlessly chic shades. Imagine ocean breezes, pastel sunsets, and delicate florals—your colors should feel calm, refined, and timelessly beautiful.",
          "Wear this: Powdery pastels, cool neutrals, soft blues, dusty pinks, and muted purples that enhance your effortless elegance.",
        ].map((suggestion) =>
          suggestion
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB")
        ),
        SeasonTags: ["Neutrals", "Cool Shades", "Cool pastels"],
      },
    },
    bodyType: {
      TRAPEZOID: {
        BodyTypeSuggestions:
          `With naturally balanced proportions and a defined waist, your goal is to enhance your shape without losing that perfect symmetry.
 
👕 Top Fit Rules: Semi-fitted, natural waist emphasis, medium-length, refined patterns.
👖 Bottom Fit Rules: Straight or slight flare, mid-rise, minimal patterns, balanced width.
🚫 Avoid this: Oversized tops, overly tight fits, or anything that throws off proportion.`
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB"),
        BodyTypeTags: {
          FitTags: ["semi-fitted", "straight"],
          PatternTags: ["solid", "small prints", "stripes", "minimal"],
        },
      },
      TRIANGLE: {
        BodyTypeSuggestions:
          `With broader hips and a narrower upper body, your goal is to add structure to your shoulders and keep the lower half sleek. Think sharp, balanced, and elongating!
 
👕 Top Fit Rules: Structured shoulders, fitted cuts, bold vertical patterns, layered textures for depth.
👖 Bottom Fit Rules: Straight or boot-cut pants, dark colors, minimal patterns, elongating lines.
🚫 Avoid this: Skinny pants, tight tops, or excessive lower-body volume—they throw off proportion.`
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB"),
        BodyTypeTags: {
          FitTags: ["straight", "bootcut", "structured"],
          PatternTags: ["vertical", "big prints"],
        },
      },
      INVERTED_TRIANGLE: {
        BodyTypeSuggestions:
          `Your broad shoulders and narrow waist give you a naturally athletic shape, but adding flow and balance is key. The goal? Ease up top and build volume below.
 
👕 Top Fit Rules: Relaxed fits, soft draping, minimal structure, simple patterns that don't overwhelm.
👖 Bottom Fit Rules: Medium to wide-leg pants, patterns allowed, horizontal details, added volume.
🚫 Avoid this: Shoulder emphasis, tight bottoms, narrow-leg pants—they make you look too top-heavy.`
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB"),
        BodyTypeTags: {
          FitTags: ["relaxed", "minimal", "soft", "wide legs"],
          PatternTags: ["horizontal", "simple", "small prints"],
        },
      },
      RECTANGLE: {
        BodyTypeSuggestions:
          `With evenly aligned shoulders, waist, and hips, you need dimension and structure to create a more dynamic, shapely look.
 
👕 Top Fit Rules: Structured fits, shoulder emphasis, defined waist, prints that create movement.
👖 Bottom Fit Rules: Flared or wide-leg pants, bold patterns, added hip volume, layered textures.
🚫 Avoid this: Boxy fits, shapeless designs, outfits that lack contrast—they make you look flat.`
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB"),
        BodyTypeTags: {
          FitTags: ["wide legged", "structured", "layered"],
          PatternTags: ["abstract", "candy stripes", "diagonal", "bold"],
        },
      },
      OVAL: {
        BodyTypeSuggestions:
          `With a fuller midsection, you want to elongate, define, and smooth out the silhouette for a polished, effortless look.
 
👕 Top Fit Rules: Unstructured cuts, longer lengths, vertical patterns, lightweight fabrics that move.
👖 Bottom Fit Rules: Straight or regular fits, dark tones, clean lines, minimal details.
🚫 Avoid this: Clingy fabrics, waist belts, heavy patterns—they draw attention to the midsection.`
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB"),
        BodyTypeTags: {
          FitTags: ["straight", "regular", "clean", "longline"],
          PatternTags: ["vertical stripes"],
        },
      },
    },
    height: {
      SHORT: {
        HeightSuggestions:
          `Keep it stylish and sharp! Choose small to medium accessories, subtle prints, and well-fitted bottoms to enhance your frame. 
                         Oversized pieces can overwhelm your frame, making you look shorter.`
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB"),
        HeightTags: ["small prints", "self design"],
      },
      MEDIUM: {
        HeightSuggestions:
          `You are lucky. Any bag size, design, or accessory works effortlessly, and you can rock any print or pattern with confidence.`
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB"),
        HeightTags: ["all patterns"],
      },
      TALL: {
        HeightSuggestions:
          `Go bold and balanced! Statement accessories, and bold design elements suit you best. 
                        Stick to medium to large prints—about the size of your open hand for a flattering look.`
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB"),
        HeightTags: ["large prints"],
      },
    },
  },
  FEMALE: {
    season: {
      SPRING: {
        ColorPaletteUrl:
          "https://monovaoutfits.blob.core.windows.net/color-palette/Spring_Palette.png",
        SeasonSuggestions: [
          "With your warm undertone, your your colour season is Spring. It's is all about radiance, energy, and life! Imagine sunny days, tropical waters, and blooming flowers—your colors should be just as vibrant and uplifting.",
          "Wear this: Warm pastels, juicy citrus tones, golden yellows, clear blues, and lively pinks that make you glow.",
        ].map((suggestion) =>
          suggestion
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB")
        ),
        SeasonTags: ["Warm pastels", "Cool pastels", "Warm shades"],
      },
      AUTUMN: {
        ColorPaletteUrl:
          "https://monovaoutfits.blob.core.windows.net/color-palette/Autumn_Palette.png",
        SeasonSuggestions: [
          "With a warm undertone, your colour season, Autumn, is all about depth, warmth, and richness. Think golden forests, spiced lattes, and cozy evenings by the fire—your palette is effortlessly stunning and sophisticated.",
          "Wear this: Earthy neutrals, golden undertones, deep greens, warm reds, and rich browns that bring out your natural warmth.",
        ].map((suggestion) =>
          suggestion
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB")
        ),
        SeasonTags: ["Neutrals", "Warm shades", "Cool Shades"],
      },
      WINTER: {
        ColorPaletteUrl:
          "https://monovaoutfits.blob.core.windows.net/color-palette/Winter_Palette.png",
        SeasonSuggestions: [
          "With a cool undertone, your colour season, winter, is all about sharp contrasts, rich depth, and undeniable presence. Think crisp snow, midnight skies, and striking jewel tones—your palette is designed to make an impact.",
          "Wear this: Crisp cool tones, deep jewel hues, high-contrast neutrals, icy brights, and intense blues that command attention.",
        ].map((suggestion) =>
          suggestion
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB")
        ),
        SeasonTags: ["Cool shades"],
      },
      SUMMER: {
        ColorPaletteUrl:
          "https://monovaoutfits.blob.core.windows.net/color-palette/Summer_Palette.png",
        SeasonSuggestions: [
          "You have a cool undertone, and your Summer season thrives in soft, serene, and effortlessly chic shades. Imagine ocean breezes, pastel sunsets, and delicate florals—your colors should feel calm, refined, and timelessly beautiful.",
          "Wear this: Powdery pastels, cool neutrals, soft blues, dusty pinks, and muted purples that enhance your effortless elegance.",
        ].map((suggestion) =>
          suggestion
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB")
        ),
        SeasonTags: ["Neutrals", "Cool Shades", "Cool pastels"],
      },
    },
    bodyType: {
      HOURGLASS: {
        BodyTypeSuggestions:
          `Your naturally balanced proportions are a dream—your goal is to enhance your waist while keeping everything proportioned and fluid.
 
👕 Top Fit Rules: Semi-fitted, natural waist emphasis, medium-length, small-to-medium patterns.
👖 Bottom Fit Rules: Straight or slight flare, mid-rise, minimal patterns, balanced width.
🚫 Avoid this: Oversized tops, super tight fits, or boxy shapes—they disrupt your natural curves.`
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB"),
        BodyTypeTags: {
          FitTags: ["semi-fitted", "straight", "mid-rise"],
          PatternTags: ["self design"],
        },
      },
      PEAR: {
        BodyTypeSuggestions:
          `With wider hips and a narrower upper body, your focus is on enhancing the top while keeping the lower half sleek and elongated.
 
👕 Top Fit Rules: Fitted shoulders, structured tops, bold patterns, under-bust emphasis.
👖 Bottom Fit Rules: Straight or boot-cut pants, dark colors, elongating lines, minimal patterns.
🚫 Avoid this: Heavy bottom details, skinny pants, or shapeless tops—they exaggerate imbalance.`
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/��/g, "\\uD83D\\uDEAB"),
        BodyTypeTags: {
          FitTags: ["straight", "boot cut"],
          PatternTags: ["self design", "vertical stripes"],
        },
      },
      INVERTED_TRIANGLE: {
        BodyTypeSuggestions:
          `Your broad shoulders and narrower hips create a striking silhouette, but the goal is to bring balance by softening the top and adding volume below.
 
👕 Top Fit Rules: Relaxed fits, simple patterns, soft draping, minimal structure.
👖 Bottom Fit Rules: Medium to wide-leg pants, horizontal details, patterns that add volume.
🚫 Avoid this: Shoulder emphasis, tight bottoms, or narrow pants—they make your top half overpowering.`
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB"),
        BodyTypeTags: {
          FitTags: ["relaxed", "wide"],
          PatternTags: ["horizontal stripes"],
        },
      },
      RECTANGLE: {
        BodyTypeSuggestions:
          `Your shoulders, waist, and hips are evenly aligned, so adding curves and structure is key to creating visual movement.
 
👕 Top Fit Rules: Structured fits, defined waist, bold patterns, layering to add shape.
👖 Bottom Fit Rules: Flared or wide-leg pants, hip emphasis, patterns that create curves.
🚫 Avoid this: Shapeless outfits, plain straight lines—they make you look boxy.`
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB"),
        BodyTypeTags: {
          FitTags: ["flared", "wide", "structured", "peplum"],
          PatternTags: ["big patterns", "geometric", "tropical", "floral"],
        },
      },
      APPLE: {
        BodyTypeSuggestions:
          `A fuller midsection calls for a longer, flowing silhouette with structured elegance to create a smooth and polished look.
 
👕 Top Fit Rules: Fluid, unstructured cuts, long lengths, vertical patterns, no clingy fabrics.
👖 Bottom Fit Rules: Straight or regular fits, dark colors, smooth lines, minimal details.
🚫 Avoid this: Waist belts, tight fabrics, or heavy patterns—they highlight the midsection.`
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB"),
        BodyTypeTags: {
          FitTags: ["longline", "straight", "regular"],
          PatternTags: ["vertical stripes", "minimal"],
        },
      },
    },
    height: {
      SHORT: {
        HeightSuggestions:
          `Stay chic and balanced! Go for small to medium bags, delicate prints, and sleek bottoms to flatter your shape. 
                         Bigger pieces can overwhelm your look.`
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB"),
        HeightTags: ["small prints", "self design"],
      },
      MEDIUM: {
        HeightSuggestions:
          `You are lucky as any bag size, design, or accessory flatters you, and you can pull off any print or pattern with ease.`
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/��/g, "\\uD83D\\uDEAB"),
        HeightTags: ["all patterns"],
      },
      TALL: {
        HeightSuggestions:
          `Make a statement! Medium to large bags, standout accessories, and bold details enhance your style. 
                        Medium to large prints—around the size of your open hand create the perfect balance.`
            .replace(/👕/g, "\\uD83D\\uDC5F")
            .replace(/👖/g, "\\uD83D\\uDC57")
            .replace(/🚫/g, "\\uD83D\\uDEAB"),
        HeightTags: ["large prints", "tropical", "floral"],
      },
    },
  },
};

// Function to get a random element from an array
function getRandomElement<T>(array: T[]): T | undefined {
  if (array.length === 0) return undefined; // Return undefined if the array is empty
  const randomIndex = Math.floor(Math.random() * array.length);
  return array[randomIndex];
}

function buildSearchQuery(
  seasonTags: string[],
  bodyTypeTags: { FitTags: string[]; PatternTags: string[] },
  heightTags: string[]
): string {
  // Build natural language query
  const selectedTags = [
    getRandomElement(seasonTags), // Random season tag
    getRandomElement(bodyTypeTags.FitTags), // Random fit tag
    getRandomElement(bodyTypeTags.PatternTags), // Random pattern tag
    getRandomElement(heightTags), // Random height tag
  ]
    .filter((tag) => tag !== undefined) // Filter out any undefined values
    .map((tag) => tag.toLowerCase()); // Convert to lowercase

  // Log the selected tags for debugging
  console.log(
    "[buildSearchQuery] Selected tags for search query:",
    selectedTags
  );

  // Join with spaces for natural language search
  return selectedTags.join(" ");
}

// Helper function
export async function getStyleGuideResponse(
  gender: Gender,
  bodyType: BodyType,
  season: Season,
  height: Height
): Promise<StyleGuideResponse> {
  console.log("[getStyleGuideResponse] Received parameters:", {
    gender,
    bodyType,
    season,
    height,
  });

  const data = styleGuideData[gender];

  // Type guard to ensure bodyType matches gender
  const isMaleBodyType = (type: BodyType): type is MaleBodyType => {
    return Object.keys(styleGuideData.MALE.bodyType).includes(type);
  };

  const isFemaleBodyType = (type: BodyType): type is FemaleBodyType => {
    return Object.keys(styleGuideData.FEMALE.bodyType).includes(type);
  };

  // Validate body type matches gender
  if (
    (gender === "MALE" && !isMaleBodyType(bodyType)) ||
    (gender === "FEMALE" && !isFemaleBodyType(bodyType))
  ) {
    console.error(
      `[getStyleGuideResponse] Invalid body type ${bodyType} for gender ${gender}`
    );
    throw new Error(`Invalid body type ${bodyType} for gender ${gender}`);
  }

  const bodyTypeData =
    gender === "MALE"
      ? (data as MasculineStyleGuideData).bodyType[bodyType as MaleBodyType]
      : (data as FeminineStyleGuideData).bodyType[bodyType as FemaleBodyType];

  const searchQuery = buildSearchQuery(
    data.season[season].SeasonTags,
    bodyTypeData.BodyTypeTags,
    data.height[height].HeightTags
  );

  console.log("[getStyleGuideResponse] Search query built:", searchQuery);

  const apparelProfile = gender === "FEMALE" ? "Women" : "Men";

  const searchResults = await apparelCollectionService.searchApparels(
    searchQuery,
    undefined,
    1,
    10,
    apparelProfile
  );

  console.log(
    "[getStyleGuideResponse] Search results received:",
    searchResults
  );

  const mediaUrls = searchResults.results
    .slice(0, 4)
    .map((result: { imageUrl: string }) => result.imageUrl);

  console.log("[getStyleGuideResponse] Media URLs extracted:", mediaUrls);

  return {
    StyleGuideMediaUrls: mediaUrls,
    SeasonOutput: {
      ColorPaletteUrl: data.season[season].ColorPaletteUrl,
      SeasonSuggestions: data.season[season].SeasonSuggestions,
      SeasonTags: data.season[season].SeasonTags,
    },
    HeightOutput: {
      HeightSuggestions: data.height[height].HeightSuggestions,
      HeightTags: data.height[height].HeightTags,
    },
    BodyOutput: {
      BodyTypeSuggestions: bodyTypeData.BodyTypeSuggestions,
      BodyTypeTags: bodyTypeData.BodyTypeTags,
    },
  };
}
