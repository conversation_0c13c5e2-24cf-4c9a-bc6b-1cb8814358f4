export interface PromptTemplate {
  Prompt: string;
  Image: string;
  UseCase: "REVIEW" | "CURATION" | "WARDROBE";
}

export const curationTemplates: PromptTemplate[] = [
  {
    Prompt: "Have your office outfit ready",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/CURATION_OFFICE2.webp",
    UseCase: "CURATION",
  },
  {
    Prompt: "Suggest outfit for office",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/CURATION_OFFICE3.webp",
    UseCase: "CURATION",
  },
  {
    Prompt: "Get ready for wedding",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/CURATION_WEDDING1.webp",
    UseCase: "CURATION",
  },
  {
    Prompt: "Outfit for friends wedding",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/CURATION_WEDDING2.webp",
    UseCase: "CURATION",
  },
  {
    Prompt: "Dinner with family",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/CURATION_DINNER1.webp",
    UseCase: "CURATION",
  },
  {
    Prompt: "Get ready for dinner date",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/CURATION_DATE1.webp",
    UseCase: "CURATION",
  },
  {
    Prompt: "Suggest outfit for a coffee date",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/CURATION_DATE2.webp",
    UseCase: "CURATION",
  },
  {
    Prompt: "Outfit for business lunch",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/CURATION_LUNCH1.webp",
    UseCase: "CURATION",
  },
  {
    Prompt: "Suggestion for travel wear",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/CURATION_TRAVEL1.webp",
    UseCase: "CURATION",
  },
  {
    Prompt: "Party tonight? Get outfit",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/CURATION_PARTY1.webp",
    UseCase: "CURATION",
  },
  {
    Prompt: "Suggest outfit for party",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/CURATION_PARTY2.webp",
    UseCase: "CURATION",
  },
  {
    Prompt: "Outfit for evening hangout",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/CURATION_EVENING1.webp",
    UseCase: "CURATION",
  },
  {
    Prompt: "Suggest outfit to beat the Monday blues",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/CURATION_MONDAY1.webp",
    UseCase: "CURATION",
  },
];

export const reviewTemplates: PromptTemplate[] = [
  {
    Prompt: "Fit check for office",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/REVIEW_OFFICE1.webp",
    UseCase: "REVIEW",
  },
  {
    Prompt: "Fit check for date night",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/REVIEW_DATE1.webp",
    UseCase: "REVIEW",
  },
  {
    Prompt: "Fit check for work lunch",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/REVIEW_LUNCH1.webp",
    UseCase: "REVIEW",
  },
  {
    Prompt: "Fit check for party night",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/REVIEW_PARTY1.webp",
    UseCase: "REVIEW",
  },
  {
    Prompt: "Outfit vibe match for wedding",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/REVIEW_WEDDING1.webp",
    UseCase: "REVIEW",
  },
  {
    Prompt: "Outfit vibe match for party",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/REVIEW_PARTY2.webp",
    UseCase: "REVIEW",
  },
  {
    Prompt: "Is this travel appropriate?",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/REVIEW_TRAVEL1.webp",
    UseCase: "REVIEW",
  },
  {
    Prompt: "Is this business meeting appropriate?",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/REVIEW_BUSINESS1.webp",
    UseCase: "REVIEW",
  },
  {
    Prompt: "Outfit approved for date?",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/REVIEW_DATE2.webp",
    UseCase: "REVIEW",
  },
];

export const wardrobeTemplates: PromptTemplate[] = [
  {
    Prompt: "Add your favs to the wardrobe",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/WARDROBE_ADD1.webp",
    UseCase: "WARDROBE",
  },
  {
    Prompt: "Upgrade your look!",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/WARDROBE_ADD2.webp",
    UseCase: "WARDROBE",
  },
  {
    Prompt: "Add your closet essentials",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/WARDROBE_ADD3.webp",
    UseCase: "WARDROBE",
  },
  {
    Prompt: "Curate your digital collection",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/WARDROBE_ADD4.webp",
    UseCase: "WARDROBE",
  },
  {
    Prompt: "Add your favourite to the wardrobe",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/WARDROBE_ADD5.webp",
    UseCase: "WARDROBE",
  },
  {
    Prompt: "Add to wardrobe",
    Image:
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/WARDROBE_ADD6.webp",
    UseCase: "WARDROBE",
  },
];